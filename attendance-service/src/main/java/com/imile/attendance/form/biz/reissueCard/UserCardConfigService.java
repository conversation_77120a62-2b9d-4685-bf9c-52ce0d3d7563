package com.imile.attendance.form.biz.reissueCard;

import com.imile.attendance.form.UserCycleReissueCardCountManage;
import com.imile.attendance.infrastructure.repository.form.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/16
 * @Description 补卡次数配置服务
 */
@Slf4j
@Service
public class UserCardConfigService {

    @Resource
    private UserCycleReissueCardCountManage cycleReissueCardCountManage;

    /**
     * 查询用户的所有考勤周期的补卡次数配置
     */
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        return cycleReissueCardCountManage.selectByUserIdList(userIdList);
    }
}
