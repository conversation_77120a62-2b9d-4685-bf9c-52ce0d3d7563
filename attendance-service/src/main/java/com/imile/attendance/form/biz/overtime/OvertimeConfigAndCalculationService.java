package com.imile.attendance.form.biz.overtime;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.form.biz.overtime.param.OverTimeCalcParam;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.util.DateHelper;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9
 * @Description 加班配置及计算服务
 */
@Slf4j
@Service
public class OvertimeConfigAndCalculationService {

    @Resource
    private AttendanceUserService userService;

    @Value(value = "${overtime.country:CHN}")
    private String overTimeCountry;

    /**
     * 预计加班时长计算
     */
    public BigDecimal overtimeCalculate(OverTimeCalcParam param){
        BigDecimal diffHour = calculateWorkHours(param.getOverTimeStartDate(), param.getOverTimeEndDate());
        //Louis产品要求，大于8小时按8小时算。
        if (diffHour.compareTo(BigDecimal.valueOf(8)) >= 0) {
            return BigDecimal.valueOf(8);
        }
        //Louis产品要求，按步长0.5向下取整。
        BigDecimal halfHour = BigDecimal.valueOf(0.5);
        if (diffHour.compareTo(halfHour) < 0) return BigDecimal.ZERO;
        BigDecimal priNumber = BigDecimal.valueOf(diffHour.intValue());
        if (diffHour.subtract(priNumber).compareTo(halfHour) >= 0) {
            return priNumber.add(halfHour);
        }
        return priNumber;
    }


    /**
     * 根据常住地校验加班权限
     */
    public Boolean checkUserOverTimeAuth(){
        Long userId = RequestInfoHolder.getUserId();
        if (Objects.isNull(userId)) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (Objects.isNull(attendanceUser)) {
            return false;
        }
        String locationCountry = attendanceUser.getLocationCountry();
        if (Objects.isNull(locationCountry)) {
            throw BusinessException.get(ErrorCodeEnum.COUNTRY_CODE_NOT_EXISTS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.COUNTRY_CODE_NOT_EXISTS.getDesc()));
        }
        List<String> locationCountryList = Arrays.asList(overTimeCountry.split(","));
        return locationCountryList.contains(locationCountry);
    }


    private BigDecimal calculateWorkHours(Date startTime, Date endTime) {
        //Louis产品要求，中国区暂时写死午休时间 12:00- 13:30
        Date lunchStart = getSpecificTime(startTime, 12, 0);
        Date lunchEnd = getSpecificTime(startTime, 13, 30);
        //计算当天工作时长（小时）
        BigDecimal totalMinutesBd = DateHelper.diffMins(endTime, startTime);
        // 判断是否需要去除午休时间
        if (startTime.before(lunchEnd) && endTime.after(lunchStart)) {
            // 计算午休时间的重叠部分
            Date overlapStart = startTime.after(lunchStart) ? startTime : lunchStart;
            Date overlapEnd = endTime.before(lunchEnd) ? endTime : lunchEnd;
            BigDecimal lunchOverlapMinutesBd = DateHelper.diffMins(overlapEnd, overlapStart);

            // 去除午休时间后的实际工作时间
            totalMinutesBd = totalMinutesBd.subtract(lunchOverlapMinutesBd);
        }
        return totalMinutesBd.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
    }

    private Date getSpecificTime(Date date, int hourOfDay, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
