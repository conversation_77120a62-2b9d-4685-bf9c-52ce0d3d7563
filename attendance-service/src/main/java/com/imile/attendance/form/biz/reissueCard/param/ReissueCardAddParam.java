package com.imile.attendance.form.biz.reissueCard.param;

import com.imile.attendance.form.param.BaseFormParam;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-27
 * @version: 1.0
 */
@Data
public class ReissueCardAddParam extends BaseFormParam {

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 补卡日期(即要处理的哪天的异常考勤日期)
     */
    private Long reissueCardDayId;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 剩余可用补卡次数
     */
    private Integer residueReissueCardCount;

    /**
     * 当前补卡日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 当前补卡日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;

    /**
     * 打卡规则对应的班次的所有的时刻信息
     */
    private String punchConfigClassItemInfo;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 本次补卡日期对应的用户补卡配置
     */
    private UserCycleReissueCardCountDO userCardConfigDO;

    /**
     * 补卡日期对应班次id
     */
    private Long reissueCardClassId;

    /**
     * 补卡日期对应打卡规则id
     */
    private Long reissueCardConfigId;

    /**
     * 当日最早实际打卡时间
     */
    private Date earlyPunchTime;

    /**
     * 当日最晚实际打卡时间
     */
    private Date latePunchTime;
}
