package com.imile.attendance.form.biz.reissueCard;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.form.UserCycleReissueCardCountManage;
import com.imile.attendance.form.biz.reissueCard.param.ReissueCardAddParam;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigRangeByDateQuery;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/16
 * @Description 补卡次数配置服务
 */
@Slf4j
@Service
public class UserReissueCardCountService {

    @Resource
    private UserCycleReissueCardCountManage cycleReissueCardCountManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;

    /**
     * 查询用户的所有考勤周期的补卡次数配置
     */
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        return cycleReissueCardCountManage.selectByUserIdList(userIdList);
    }


    /**
     * 获取用户的剩余补卡次数
     *
     * @param param
     * @param reissueCardDate
     * @return
     */
    public int getUserResidueReissueCardCount(ReissueCardAddParam param, Date reissueCardDate) {
        ReissueCardConfigRangeByDateQuery configRangeByDateQuery = ReissueCardConfigRangeByDateQuery
                .builder()
                .userIds(Arrays.asList(param.getUserId()))
                .startDate(DateUtil.endOfDay(reissueCardDate))
                .endDate(DateUtil.endOfDay(reissueCardDate))
                .build();
        List<ReissueCardConfigRangeDO> reissueCardConfigRangeList = reissueCardConfigManage.selectConfigRangeByDate(configRangeByDateQuery)
                .stream()
                .sorted(Comparator.comparing(ReissueCardConfigRangeDO::getExpireTime).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reissueCardConfigRangeList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getDesc()));
        }
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigManage.getReissueCardConfigByIds(
                Collections.singletonList(reissueCardConfigRangeList.get(0).getRuleConfigId()));
        if (CollectionUtils.isEmpty(reissueCardConfigDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getDesc()));
        }
        Integer maxRepunchNumber = reissueCardConfigDOList.get(0).getMaxRepunchNumber();
        if (maxRepunchNumber == null) {
            maxRepunchNumber = 0;
        }
        List<UserCycleReissueCardCountDO> userCardConfigDOS = selectByUserIdList(Arrays.asList(param.getUserId()));
        if (CollectionUtils.isEmpty(userCardConfigDOS)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getDesc()));
        }
        //查询用户本次异常考勤处于的打卡规则配置中的已补卡次数
        List<UserCycleReissueCardCountDO> userCycleReissueCardCountDOS = userCardConfigDOS.stream()
                .filter(item -> item.getCycleStartDate().compareTo(reissueCardDate) < 1 && item.getCycleEndDate().compareTo(reissueCardDate) > -1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCycleReissueCardCountDOS)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG.getDesc()));
        }
        param.setUserCardConfigDO(userCycleReissueCardCountDOS.get(0));
        Integer usedCardCount = userCycleReissueCardCountDOS.get(0).getUsedReissueCardCount();
        if (usedCardCount == null) {
            usedCardCount = 0;
        }
        return (int) (maxRepunchNumber - usedCardCount);
    }
}
