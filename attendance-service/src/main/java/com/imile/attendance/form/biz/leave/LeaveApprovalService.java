package com.imile.attendance.form.biz.leave;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.approval.LeaveCustomFieldEnum;
import com.imile.attendance.enums.form.ApplicationDataSourceEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.enums.vacation.LeaveUnitEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.leave.param.LeaveAddParam;
import com.imile.attendance.form.biz.leave.vo.LeaveFormInfoExportVO;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.dto.UserAuthDTO;
import com.imile.attendance.form.mapstruct.AttendanceFormMapstruct;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.DictService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/7
 * @Description 请假审批服务
 */
@Slf4j
@Service
public class LeaveApprovalService {

    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private IdWorkUtils idWorkUtils;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private DictService dictService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private AttendanceFormManage formManage;

    /**
     * 请假申请(点击新增按钮调用 新增/暂存)
     *
     * @param param
     * @return
     */
    public ApprovalResultVO leaveAdd(LeaveAddParam param) {
        log.info("leaveAdd | LeaveAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        // 现在该接口不存在operationType = 2的情况，预览是新的接口了
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1. 封装单据参数
        commonFormOperationService.userBaseInfoBuild(param, null, null);
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> applicationFormAttrArrayList = new ArrayList<>();
        List<AttendanceFormRelationDO> applicationFormRelationArrayList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 需要扣减假期余额、增加已使用余额的假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 用户请假记录数据
        UserLeaveRecordDO userLeaveRecord = null;
        // 2. 保存逻辑及业务校验
        if (param.getOperationType() == 1) {
            //异常判断
            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
            // 新增返回请假总时长-单位分钟
            BigDecimal totalLeaveTime = leaveAddDataCheck(param);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
            // 创建用户请假详情信息数据
            commonFormOperationService.buildUserLeaveStageDetailList(userLeaveStageDetailList, param, totalLeaveTime);
            // 创建用户请假记录数据
            userLeaveRecord = commonFormOperationService.buildUserLeaveRecord(totalLeaveTime, param, LeaveTypeEnum.LEAVE.getCode(), "【hrms或clover】新增请假申请入口");
        }
        // 3. 构建表单信息
        //注意，不仅需要构建审批表信息，正常考勤表也需要落库，状态为未生效,注意假期比例也是为空，只有审批通过，才会扣除假期
        this.leaveDataAddBuild(param, formDO, applicationFormRelationArrayList, applicationFormAttrArrayList
                , employeeAbnormalOperationRecord, abnormalAttendanceDO);
        // 4. 暂存逻辑 暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //暂存不用落正常考勤表/异常表
            attendanceApprovalManage.formAdd(formDO, applicationFormRelationArrayList, applicationFormAttrArrayList
                    , null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        // 5. bpm审批流构建
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.leaveAddApprovalDataBuild(initInfoApiDTO, formDO, applicationFormAttrArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 6. 落库
        attendanceApprovalManage.formAdd(formDO, applicationFormRelationArrayList, applicationFormAttrArrayList
                , employeeAbnormalOperationRecord, abnormalAttendanceDO, userLeaveStageDetailList, userLeaveRecord);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 请假申请(暂存更新/重提交)
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO leaveUpdate(LeaveAddParam param) {
        log.info("leaveUpdate | LeaveAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1. 封装单据参数
        commonFormOperationService.userBaseInfoBuild(param, null, null);
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> formAttrDOArrayList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 需要扣减假期余额、增加已使用余额的假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 用户请假记录数据
        UserLeaveRecordDO userLeaveRecord = null;
        // 2. 保存逻辑及业务校验
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
            // 新增返回请假总时长-单位分钟
            BigDecimal totalLeaveTime = leaveAddDataCheck(param);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
            commonFormOperationService.buildUserLeaveStageDetailList(userLeaveStageDetailList, param, totalLeaveTime);
            // 创建用户请假记录数据
            userLeaveRecord = commonFormOperationService.buildUserLeaveRecord(totalLeaveTime, param, LeaveTypeEnum.LEAVE.getCode(), "【hrms或clover驳回重新提交 或 hrms暂存重新提交】修改请假申请入口");
        }
        // 3. 构建表单信息
        this.leaveDataUpdateBuild(param, formDO, formRelationDOList, formAttrDOArrayList, employeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        // 4. 暂存逻辑 暂存不需要调用bpm暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            commonFormOperationService.formUpdate(formDO, formDetailBO, attendanceApprovalManage);
            attendanceApprovalManage.formAdd(null, formRelationDOList, formAttrDOArrayList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.leaveAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOArrayList);
        //落库 校验本次保存是否是驳回后重提
        commonFormOperationService.updateFormBuild(formDO, formDetailBO, initInfoApiDTO, resultVO);
        attendanceApprovalManage.formAdd(null, formRelationDOList, formAttrDOArrayList, employeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, userLeaveRecord);
        return resultVO;
    }

    /**
     * 请假-撤销申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO leaveRevokeAdd(RevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        AttendanceFormDetailBO formDetailBO;
        if (param.getApplicationFormId() != null) {
            formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = formManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO leaveFormDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        Long userId = leaveFormDO.getUserId();
        List<String> employeeTypeList = Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode());
        commonFormOperationService.checkMexUser(userId, ErrorCodeEnum.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK, employeeTypeList);

        // 撤销校验
        AttendanceFormAttrDO leaveStartDate = attrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        commonFormOperationService.revokeCheck(userId, leaveFormDO.getId(), leaveStartDate);

        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOArrayList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();
        // 【请假审核通过】销假：重新生成一条请假销假单，创建请假销假单与原请假单的关联。所以不需要操作假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, formDO, formRelationDOS, formAttrDOArrayList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE_REVOKE), FormTypeEnum.LEAVE_REVOKE.getCode());

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.leaveRevokeAddApprovalDataBuild(initInfoApiDTO, leaveFormDO.getApprovalId(), formDO, formAttrDOArrayList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        attendanceApprovalManage.formAdd(formDO, formRelationDOS, formAttrDOArrayList, null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 请假申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> leavePreview(LeaveAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }

        //暂存就是保存前一步，必填数据都要填写完毕才可以
        commonFormOperationService.userBaseInfoBuild(param, null, null);
        //异常判断
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
        leaveAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO EmployeeAbnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        this.leaveDataAddBuild(param, formDO, formRelationDOList, formAttrDOList, EmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.leaveAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = AttendanceFormMapstruct.INSTANCE.mapInitInfoToRecordApiQuery(initInfoApiDTO);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        // 构建预览实体
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 请假-撤销申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> leaveRevokePreview(RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO leaveFormDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));

        // 撤销校验
        AttendanceFormAttrDO leaveStartDate = attrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
        commonFormOperationService.revokeCheck(leaveFormDO.getUserId(), leaveFormDO.getId(), leaveStartDate);

        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, formDO, formRelationDOS, formAttrDOList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE_REVOKE), FormTypeEnum.LEAVE_REVOKE.getCode());
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        leaveAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = AttendanceFormMapstruct.INSTANCE.mapInitInfoToRecordApiQuery(initInfoApiDTO);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        // 构建预览实体
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, leaveFormDO.getUserCode());
        return resultDTOList;
    }

    /**
     * 请假导出
     *
     * @param param
     * @return
     */
    public PaginationResult<LeaveFormInfoExportVO> listExport(AttendanceApprovalInfoParam param) {
        if (StringUtils.isNotBlank(param.getDeptIdString())) {
            List<String> deptIdStringList = Arrays.asList(param.getDeptIdString().split(","));
            List<Long> deptIdList = new ArrayList<>();
            deptIdStringList.forEach(item -> {
                deptIdList.add(Long.valueOf(item));
            });
            param.setDeptIds(deptIdList);
        }
        AttendanceApprovalInfoQuery query = AttendanceFormMapstruct.INSTANCE.mapInitInfoToApprovalApiQuery(param);
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "HRMS")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = commonFormOperationService.userDeptAuthList(userAuthParam);
            if (!commonFormOperationService.checkDeptAuth(query, userAuthDTO)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
        }
        if (StringUtils.equalsIgnoreCase(param.getDataSource(), "CLOVER")) {
            query.setUserIdList(Arrays.asList(RequestInfoHolder.getUserId()));
        }
        if (StringUtils.isNotBlank(param.getFormStatus())) {
            query.setFormStatusList(Arrays.asList(param.getFormStatus()));
        }
        query.setExcludeApplicationDataSource(ApplicationDataSourceEnum.IMPORT.getCode());
        Page<AttendanceFormDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getShowCount() > 0);
        PageInfo<AttendanceFormDO> pageInfo = page.doSelectPageInfo(() -> formManage.selectAttendanceApprovalInfo(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
//            PageInfo<LeaveFormInfoExportVO> pageInfoResult = BeanUtils.convert(new PageInfo<>(), PageInfo.class);
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> formIdList = pageInfo.getList().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceFormAttrDO> attrDOList = formManage.selectFormAttrByFormIdLit(formIdList);
        List<Long> uesrIdList = pageInfo.getList().stream().map(item -> item.getUserId()).collect(Collectors.toList());
        List<Long> applyUesrIdList = pageInfo.getList().stream().map(item -> item.getApplyUserId()).collect(Collectors.toList());
        List<Long> allUesrIdList = new ArrayList<>();
        allUesrIdList.addAll(uesrIdList);
        allUesrIdList.addAll(applyUesrIdList);
        List<AttendanceUser> userInfoList = userService.listUsersByIds(allUesrIdList);
        List<Long> deptIdList = userInfoList.stream().filter(item -> item.getDeptId() != null).map(item -> item.getDeptId()).collect(Collectors.toList());
        List<Long> postIdList = userInfoList.stream().filter(item -> item.getPostId() != null).map(item -> item.getPostId()).collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
        List<AttendancePost> postList = postService.listByPostList(postIdList);
        List<LeaveFormInfoExportVO> exportVOList = new ArrayList<>();
        for (AttendanceFormDO formDO : pageInfo.getList()) {
            LeaveFormInfoExportVO exportVO = new LeaveFormInfoExportVO();
            exportVO.setApplicationCode(formDO.getApplicationCode());
            exportVO.setFormStatus(formDO.getFormStatus());
            exportVO.setCreateDate(formDO.getCreateDate());
            List<AttendanceFormAttrDO> leaveNameDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(leaveNameDOList)) {
                exportVO.setLeaveName(leaveNameDOList.get(0).getAttrValue());
            }
            if (query.getFormTypeList().contains(FormTypeEnum.LEAVE.getCode())) {
                List<AttendanceFormAttrDO> leaveStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveStartDateDOList)) {
                    exportVO.setStartDate(leaveStartDateDOList.get(0).getAttrValue());
                }
                List<AttendanceFormAttrDO> leaveEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(leaveEndDateDOList)) {
                    exportVO.setEndDate(leaveEndDateDOList.get(0).getAttrValue());
                }
            }
            if (query.getFormTypeList().contains(FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
                    exportVO.setStartDate(outOfOfficeStartDateDOList.get(0).getAttrValue());
                }
                List<AttendanceFormAttrDO> outOfOfficeEndDateDOList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
                    exportVO.setEndDate(outOfOfficeEndDateDOList.get(0).getAttrValue());
                }
            }
            List<AttendanceFormAttrDO> dayInfoList = attrDOList.stream().filter(item -> item.getFormId().equals(formDO.getId()) && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dayInfoList)) {
                List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

                BigDecimal days = BigDecimal.ZERO;
                BigDecimal hours = BigDecimal.ZERO;
                BigDecimal minutes = BigDecimal.ZERO;

                for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                    days = days.add(dayDurationInfoDTO.getDays());
                    hours = hours.add(dayDurationInfoDTO.getHours());
                    minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                }
                String descCN = days + "天" + hours + "小时" + minutes + "分钟";
                String descEN = days + "days" + hours + "hours" + minutes + "minutes";
                Map<String, String> expectedLeaveTimeMap = new HashMap<>();
                expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
                expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
                exportVO.setLeaveHours(descEN);
            }

            exportVOList.add(exportVO);
            List<AttendanceUser> existUserInfoList = userInfoList
                    .stream()
                    .filter(item -> item.getId().equals(formDO.getUserId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existUserInfoList)) {
                continue;
            }
            exportVO.setUserCode(existUserInfoList.get(0).getUserCode());
            exportVO.setUserName(existUserInfoList.get(0).getUserName());
            exportVO.setWorkNo(existUserInfoList.get(0).getWorkNo());
            exportVO.setCountry(existUserInfoList.get(0).getLocationCountry());
            List<AttendanceDept> existDeptDOList = deptList
                    .stream()
                    .filter(item -> item.getId().equals(existUserInfoList.get(0).getDeptId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptDOList)) {
                exportVO.setDeptName(existDeptDOList.get(0).getDeptNameEn());
            }
            List<AttendancePost> existPostDOList = postList
                    .stream()
                    .filter(item -> item.getId().equals(existUserInfoList.get(0).getPostId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existPostDOList)) {
                exportVO.setPostName(existPostDOList.get(0).getPostNameEn());
            }

            List<AttendanceUser> existApplyUserInfoDOList = userInfoList.stream().filter(item -> item.getId().equals(formDO.getApplyUserId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existApplyUserInfoDOList)) {
                continue;
            }
            exportVO.setApplyUserCode(existApplyUserInfoDOList.get(0).getUserCode());
            exportVO.setApplyUserName(existApplyUserInfoDOList.get(0).getUserName());
            exportVO.setApplyUserWorkNo(existApplyUserInfoDOList.get(0).getWorkNo());
        }
        return PageUtil.getPageResult(exportVOList, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 请假申请参数校验
     *
     * @param param
     * @return
     */
    private BigDecimal leaveAddDataCheck(LeaveAddParam param) {
        if (StringUtils.isBlank(param.getLeaveName())) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_NAME_IS_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_NAME_IS_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveStartDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveEndDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_END_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_END_DATE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isEmpty(param.getRemark())) {
            throw BusinessException.get(ErrorCodeEnum.REMARK_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.REMARK_NOT_EMPTY.getDesc()));
        }
        param.setDayAttendanceHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        // 考勤周期 校验 查询考勤周期是否超过请假时间
        commonFormOperationService.attendanceCycleCheck(new Date(), param.getUserId(), param.getLeaveStartDate());

        // 查询该假期配置
        // 修改为通过人员范围查询假期配置
        CompanyLeaveConfigDO companyLeaveConfig = companyLeaveConfigService.getById(param.getConfigId());
        param.setLeaveUnit(companyLeaveConfig.getLeaveUnit());
        param.setMiniLeaveDuration(companyLeaveConfig.getMiniLeaveDuration());
        param.setLeaveShortName(companyLeaveConfig.getLeaveShortName());
        param.setIsUploadAttachment(companyLeaveConfig.getIsUploadAttachment());
        param.setUploadAttachmentCondition(companyLeaveConfig.getUploadAttachmentCondition());
        param.setAttachmentUnit(companyLeaveConfig.getAttachmentUnit());

        // 查询该用户假期，获取用户该假期剩余可用余额
        List<UserLeaveResidualVO> userLeaveResidualVOS = userLeaveService.selectUserLeaveResidual(param.getUserId());
        List<UserLeaveResidualVO> userLeaveList = userLeaveResidualVOS.stream()
                .filter(item -> Objects.equals(item.getConfigId(), param.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userLeaveList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
        }
        param.setLeaveResidueMinutes(userLeaveList.get(0).getLeaveResidueMinutes());

        //判断是否还有冲突
        List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
        commonFormOperationService.selectClashApplication(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), clashApplicationInfoDTOList);
        if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
            throw BusinessException.get(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getCode(), I18nUtils.getMessage(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getDesc()));
        }
        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), companyLeaveConfig, dayDurationInfoDTOList);
        //过滤不占用请假时间的日期
        dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                        || item.getHours().compareTo(BigDecimal.ZERO) > 0
                        || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        param.setDayDurationInfoDTOList(dayDurationInfoDTOList);
        // 计算请假总时长-单位分钟
        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
        // 校验最小请假时长及附件条件
        checkLeaveCondition(param, dayDurationInfoDTOList);
        // 校验请假时长与请假余额
        checkLeaveTime(param, userLeaveList, totalLeaveTime);

        return totalLeaveTime;
    }

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    private BigDecimal handlerLeaveTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历每一天的请假时长
        for (DayDurationInfoDTO dayDurationInfo : dayDurationInfoList) {

            // 计算一天的请假时长-分钟（天 * 法定工作时长 8h * 一小时的分钟数）【这里面的法定时长为什么给8h：因为，假期给的时候一天是按照8h给，所以请一天假，需要扣假期8h】
            totalMinutes = totalMinutes.add(dayDurationInfo.getDays().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));

            // 换算之后的小时数【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
            BigDecimal realHours = dayDurationInfo.getHours().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP);
            // 计算小时的请假时长-分钟（小时 * 一小时的分钟数）
            totalMinutes = totalMinutes.add(realHours.multiply(BusinessConstant.MINUTES));

            /*
                 计算分钟的请假时长-分钟【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
                    算法1：分钟/60min/工作时长h * 8h = 分钟转换成8h的分钟数
                    //BigDecimal realMinutesHours = dayDurationInfo.getMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
                    算法2：分钟 * 8h * 60min / 工作时长h * 60min
                    算法1 存在很大精度问题，因为有多次除法，每一次除法都会保留两位小数，然后四舍五入，所以会存在精度问题
                    算法2 只有一次除法，所以精度问题 小很多
            */

            BigDecimal realMinutes = dayDurationInfo.getMinutes().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES).divide(dayDurationInfo.getLegalWorkingHours().multiply(BusinessConstant.MINUTES), 2, RoundingMode.HALF_UP);

            totalMinutes = totalMinutes.add(realMinutes);
        }
        return totalMinutes;
    }

    /**
     * 请假天数校验
     *
     * @param param
     * @param dayDurationInfoDTOList
     */
    private static void checkLeaveCondition(LeaveAddParam param,
                                            List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //校验天假不能请假小于1天
        if (CollectionUtils.isEmpty(dayDurationInfoDTOList)) {
            return;
        }
        // 获取请假单位及最小请假时长
        String leaveUnit = param.getLeaveUnit();
        Integer miniLeaveDuration = Objects.isNull(param.getMiniLeaveDuration())
                ? BusinessConstant.ZERO : param.getMiniLeaveDuration();
        BigDecimal totalDays = BigDecimal.ZERO;
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
            // 总天数
            totalDays = totalDays.add(dayDurationInfoDTO.getDays());
            BigDecimal legalWorkingHours = dayDurationInfoDTO.getLegalWorkingHours();
            // 总小时数
            BigDecimal dayHours = dayDurationInfoDTO.getDays().multiply(legalWorkingHours)
                    .add(dayDurationInfoDTO.getHours());
            totalHours = totalHours.add(dayHours);
            // 总分钟数
            BigDecimal dayMinutes = dayHours.multiply(BusinessConstant.MINUTES).add(dayDurationInfoDTO.getMinutes());
            totalMinutes = totalMinutes.add(dayMinutes);
            // 天假特殊校验 不能小于一天
            if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit) && dayDurationInfoDTO.getDays().compareTo(BigDecimal.ZERO) == 0) {
                throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DAY_CANNOT_BE_ZERO);
            }
        }
        // 校验请假时长必须大于最小请假时长
        // 最小请假单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit)
                && totalDays.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(leaveUnit)
                && totalHours.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(leaveUnit)
                && totalMinutes.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 校验附件是否必填
        if (!BusinessConstant.Y.equals(param.getIsUploadAttachment())) {
            return;
        }
        // 获取附件单位
        String attachmentUnit = param.getAttachmentUnit();
        // 上传附件单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(attachmentUnit)
                && totalDays.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(attachmentUnit)
                && totalHours.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(attachmentUnit)
                && totalMinutes.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }

    }

    /**
     * 校验请假时长与余额
     *
     * @param param          入参
     * @param userLeaveList  用户假期列表
     * @param totalLeaveTime 请假总时长
     */
    private static void checkLeaveTime(LeaveAddParam param,
                                       List<UserLeaveResidualVO> userLeaveList,
                                       BigDecimal totalLeaveTime) {
        // 请假时长不能为0
        if (totalLeaveTime.compareTo(BigDecimal.ZERO) == 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_TIME_CANNOT_BE_ZERO);
        }
        // 获取请假时长
        long leaveTime = DateUtil.between(param.getLeaveStartDate(), param.getLeaveEndDate(), DateUnit.MINUTE, false);
        // 请假结束时间必须在开始时间之后
        if (leaveTime < 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_LEAVE_END_TIME_MUST_BE_AFTER_THE_START_TIME);
        }

        // 1. 过滤假期余额小于等于0的历史数据： 不能请假
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_LEAVE_BALANCE_IS_INSUFFICIENT);
        }
        // 过滤完1. 在过滤请假余额不能小于请假时长，才会准确
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(totalLeaveTime) < 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_BALANCE_OF_LEAVE_CANNOT_BE_LESS_THAN_THE_LENGTH_OF_LEAVE);
        }
    }

    /**
     * 构建请假表单信息
     *
     * @param param
     * @param formDO
     * @param applicationFormRelationArrayList
     * @param applicationFormAttrArrayList
     * @param employeeAbnormalOperationRecord
     * @param abnormalAttendanceDO
     */
    private void leaveDataAddBuild(LeaveAddParam param,
                                   AttendanceFormDO formDO,
                                   List<AttendanceFormRelationDO> applicationFormRelationArrayList,
                                   List<AttendanceFormAttrDO> applicationFormAttrArrayList,
                                   EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                   EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.LEAVE.getCode());
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE));

        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);
        // 审批单字段构建
        if (Objects.nonNull(param.getConfigId())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.configID.getLowerCode(), String.valueOf(param.getConfigId())));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode(), param.getLeaveName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveShortName())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode(), param.getLeaveShortName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveUnit())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode(), param.getLeaveUnit()));
        }
        if (param.getLeaveStartDate() != null) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode(), DateUtil.format(param.getLeaveStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveEndDate() != null) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode(), DateUtil.format(param.getLeaveEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveResidueMinutes() != null) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode(), param.getLeaveResidueMinutes().toString()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }

        //默认是没有被撤销
        applicationFormAttrArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            AttendanceFormRelationDO relationDO = new AttendanceFormRelationDO();
            relationDO.setId(defaultIdWorker.nextId());
            relationDO.setFormId(formDO.getId());
            relationDO.setRelationId(param.getAbnormalId());
            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
            BaseDOUtil.fillDOInsert(relationDO);
            applicationFormRelationArrayList.add(relationDO);

            employeeAbnormalOperationRecord.setId(defaultIdWorker.nextId());
            employeeAbnormalOperationRecord.setFormId(formDO.getId());
            employeeAbnormalOperationRecord.setAbnormalId(param.getAbnormalId());
            employeeAbnormalOperationRecord.setOperationType(AbnormalOperationTypeEnum.LEAVE.getCode());
            BaseDOUtil.fillDOInsert(formDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }

    }

    private void leaveDataUpdateBuild(LeaveAddParam param,
                                      AttendanceFormDO formDO,
                                      List<AttendanceFormRelationDO> formRelationDOList,
                                      List<AttendanceFormAttrDO> formAttrDOList,
                                      EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                                      EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.LEAVE.getCode());

        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(formDO);

        if (StringUtils.isNotBlank(param.getLeaveName())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveType.getLowerCode(),
                    param.getLeaveName()));
        }
        if (Objects.nonNull(param.getConfigId())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.configID.getLowerCode(),
                    String.valueOf(param.getConfigId())));
        }
        if (StringUtils.isNotBlank(param.getLeaveShortName())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode(),
                    param.getLeaveShortName()));
        }
        if (StringUtils.isNotBlank(param.getLeaveUnit())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode(),
                    param.getLeaveUnit()));
        }
        if (param.getLeaveResidueMinutes() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode(),
                    param.getLeaveResidueMinutes().toString()));
        }
        if (param.getLeaveStartDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode(),
                    DateUtil.format(param.getLeaveStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getLeaveEndDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode(),
                    DateUtil.format(param.getLeaveEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.remark.getLowerCode(),
                    param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(),
                    JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                    ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(),
                    JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }
        //默认是没有被撤销
        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(),
                ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(),
                BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            //把异常ID也关联下
            commonFormOperationService.associateAbnormal(formDO, formRelationDOList,
                    employeeAbnormalOperationRecordDO, param.getAbnormalId());
            employeeAbnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.LEAVE.getCode());
            BaseDOUtil.fillDOInsert(formDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }

    /**
     * 请假审批参数构建
     *
     * @param initInfoApiDTO
     * @param formDO
     * @param formAttrDOList
     */
    private void leaveAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                           AttendanceFormDO formDO,
                                           List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);

        // 假期配置相关
        this.setLeaveTypeField(fieldApiDTOList, formAttrDOList);

        // 假期可用余额
        List<AttendanceFormAttrDO> leaveResidue = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveResidue)) {
            BigDecimal leaveResidueMinutes = new BigDecimal(leaveResidue.get(0).getAttrValue());
            String days = BigDecimal.valueOf(leaveResidueMinutes.longValue() / BusinessConstant.MINUTES.longValue()).divide(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).setScale(2, RoundingMode.DOWN).toString();
            String descCN = days + "天";
            String descEN = days + "days";
            Map<String, String> leaveResidueMinutesMap = new HashMap<>();
            leaveResidueMinutesMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            leaveResidueMinutesMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_RESIDUAL.getCode(), descEN, leaveResidueMinutesMap);
        }

        List<AttendanceFormAttrDO> leaveUnitDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());

        //请假开始时间
        List<AttendanceFormAttrDO> leaveStartDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveStartDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            AttendanceFormAttrDO leaveStartDateDO = leaveStartDateDOList.get(0);
            String leaveStartDateString = leaveStartDateDO.getAttrValue();
            Date leaveStartDate = DateUtil.parse(leaveStartDateString, "yyyy-MM-dd HH:mm:ss");
            leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_START_DATE.getCode(), leaveStartDateString, null);
        }

        //请假结束时间
        List<AttendanceFormAttrDO> leaveEndDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveEndDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            AttendanceFormAttrDO leaveEndDateDO = leaveEndDateDOList.get(0);
            String leaveEndDateString = leaveEndDateDO.getAttrValue();
            Date leaveEndDate = DateUtil.parse(leaveEndDateString, "yyyy-MM-dd HH:mm:ss");
            leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_END_DATE.getCode(), leaveEndDateString, null);
        }

        //备注
        List<AttendanceFormAttrDO> remark = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        this.setFileField(fieldApiDTOList, formAttrDOList);

        //请假时长（JSON）
        this.setDurationField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("leaveAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void leaveRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                                 Long leaveApprovalId,
                                                 AttendanceFormDO formDO,
                                                 List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);
        //关联请假单据的审批ID
        if (leaveApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(leaveApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);

        // 假期配置相关
        this.setLeaveTypeField(fieldApiDTOList, formAttrDOList);

        // 假期可用余额
        List<AttendanceFormAttrDO> leaveResidue = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveResidue)) {
            BigDecimal leaveResidueMinutes = new BigDecimal(leaveResidue.get(0).getAttrValue());
            Long days = BigDecimal.valueOf(leaveResidueMinutes.longValue() % BusinessConstant.MINUTES.longValue()).divide(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).setScale(2, RoundingMode.DOWN).longValue();
            String descCN = days + "天";
            String descEN = days + "days";
            Map<String, String> leaveResidueMinutesMap = new HashMap<>();
            leaveResidueMinutesMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            leaveResidueMinutesMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_RESIDUAL.getCode(), descEN, leaveResidueMinutesMap);
        }

        List<AttendanceFormAttrDO> leaveUnitDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode())).collect(Collectors.toList());
        //请假开始时间
        List<AttendanceFormAttrDO> leaveStartDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveStartDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            AttendanceFormAttrDO leaveStartDateDO = leaveStartDateDOList.get(0);
            String leaveStartDateString = leaveStartDateDO.getAttrValue();
            Date leaveStartDate = DateUtil.parse(leaveStartDateString, "yyyy-MM-dd HH:mm:ss");
            AttendanceFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
                leaveStartDateString = DateUtil.format(leaveStartDate, "yyyy-MM-dd HH:mm");
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_START_DATE.getCode(), leaveStartDateString, null);
        }

        //请假结束时间
        List<AttendanceFormAttrDO> leaveEndDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaveEndDateDOList) && CollectionUtils.isNotEmpty(leaveUnitDOList)) {
            AttendanceFormAttrDO leaveEndDateDO = leaveEndDateDOList.get(0);
            String leaveEndDateString = leaveEndDateDO.getAttrValue();
            Date leaveEndDate = DateUtil.parse(leaveEndDateString, "yyyy-MM-dd HH:mm:ss");
            AttendanceFormAttrDO leaveUnitDO = leaveUnitDOList.get(0);
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.DAYS.getCode())) {
                leaveEndDateString = DateUtil.format(DateUtil.offsetDay(leaveEndDate, -1), "yyyy-MM-dd");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.HOURS.getCode())) {
                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
            }
            if (StringUtils.equalsIgnoreCase(leaveUnitDO.getAttrValue(), LeaveUnitEnum.MINUTES.getCode())) {
                leaveEndDateString = DateUtil.format(leaveEndDate, "yyyy-MM-dd HH:mm");
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_END_DATE.getCode(), leaveEndDateString, null);
        }

        //备注
        List<AttendanceFormAttrDO> remark = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //撤销原因
        List<AttendanceFormAttrDO> revokeReason = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        //附件
        this.setFileField(fieldApiDTOList, formAttrDOList);

        //请假时长（JSON）
        this.setDurationField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("leaveRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void setLeaveTypeField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                   List<AttendanceFormAttrDO> formAttrDOList) {
        // 假期规则主键
        List<AttendanceFormAttrDO> configId = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.CONFIG_ID.getCode(), CollectionUtils.isNotEmpty(configId) ? configId.get(0).getAttrValue() : null, null);
        // 假期类型
        String leaveTypeByLang = null;
        Map<String, String> leaveTypeMap = new HashMap();
        List<AttendanceFormAttrDO> leaveType = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        // 设置异常类型中英文内容多语
        if (CollectionUtils.isNotEmpty(leaveType)) {
            leaveTypeByLang = leaveType.get(0).getAttrValue();
            Map<String, Map<String, DictVO>> allLangByTypeCodes = dictService.getAllLangByTypeCodes(Arrays.asList(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE));
            Map<String, DictVO> leaveTypeEnumMap = allLangByTypeCodes.get(BusinessConstant.SysDictDataTypeConstant.HRMS_ATTENDANCE_LEAVE_TYPE);
            Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));
            DictVO dictVO = lowerleaveTypeEnumMap.get(leaveTypeByLang.toLowerCase());
            if (Objects.nonNull(dictVO)) {
                leaveTypeByLang = dictVO.getDataValue();
                leaveTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), dictVO.getDataValueCn());
                leaveTypeMap.put(LanguageTypeEnum.en_US.getCode(), dictVO.getDataValueEn());
            }
        }
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_TYPE.getCode(), leaveTypeByLang, leaveTypeMap);

        //假期简称
        List<AttendanceFormAttrDO> leaveShortName = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_SHORT_NAME.getCode(), CollectionUtils.isNotEmpty(leaveShortName) ? leaveShortName.get(0).getAttrValue() : null, null);
    }

    private void setFileField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                              List<AttendanceFormAttrDO> formAttrDOList) {
        List<AttendanceFormAttrDO> attachment = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }
    }

    private void setDurationField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                  List<AttendanceFormAttrDO> formAttrDOList) {
        List<AttendanceFormAttrDO> dayInfoList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.EXPECTED_LEAVE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }
    }

    private void setBaseField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                              AttendanceFormDO formDO) {
        //被审批人ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), formDO.getUserId() != null ? formDO.getUserId().toString() : null, null);

        //被申请人部门ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_ID.getCode(), formDO.getDeptId() != null ? formDO.getDeptId().toString() : null, null);

        //被申请人所在国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_COUNTRY.getCode(), formDO.getCountry(), null);

        //被申请人结算国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), formDO.getOriginCountry(), null);

        //被申请人是否仓内
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.IS_WAREHOUSE_STAFF.getCode(), formDO.getIsWarehouseStaff() != null ? formDO.getIsWarehouseStaff().toString() : null, null);

        //被申请人姓名
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_NAME.getCode(), formDO.getUserName(), null);

        //被申请人编码
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.USER_CODE.getCode(), formDO.getUserCode(), null);

        //被申请人部门
        AttendanceDept deptInfo = deptService.getByDeptId(formDO.getDeptId());
        if (deptInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), deptInfo.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), deptInfo.getDeptNameEn());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.DEPT_NAME.getCode(), deptInfo.getDeptNameEn(), deptMap);

        // 设置岗位
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        // 仓内外包人员不存在岗位,所以针对仓内外包人员不设置岗位
        if (!Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(formDO.getCountry())
                || ObjectUtil.notEqual(formDO.getIsWarehouseStaff(), 1)
                || ObjectUtil.notEqual(userInfo.getEmployeeType(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
            //被申请人岗位
            AttendancePost postInfo = postService.getByPostId(formDO.getPostId());
            if (postInfo == null) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, LeaveCustomFieldEnum.POST_NAME.getCode(), postInfo.getPostNameEn(), null);
        }
    }

}
