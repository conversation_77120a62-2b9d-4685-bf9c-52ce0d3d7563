package com.imile.attendance.clock;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.clock.dto.AttendanceDayClassInfoDTO;
import com.imile.attendance.clock.dto.MobileAbnormalDTO;
import com.imile.attendance.clock.dto.MobileFormDTO;
import com.imile.attendance.clock.dto.MobilePunchCardRecordDTO;
import com.imile.attendance.clock.dto.PunchClassConfigDTO;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.dto.UserMobileRuleConfigDTO;
import com.imile.attendance.clock.mapstruct.MobileConfigMapstruct;
import com.imile.attendance.clock.query.MobileDayPunchDetailQuery;
import com.imile.attendance.clock.query.MobilePunchDetailQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AbnormalPunchStatusEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punchRecord.EmployeePunchRecordManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.infrastructure.form.FormAttrUtils;
import com.imile.attendance.util.PunchTimeCalculator;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19
 * @Description
 */
@Slf4j
@Service
public class MobilePunchDetailQueryService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private EmployeePunchRecordManage punchRecordManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private EmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;

    /**
     * 个人考勤详情
     */
    public UserDayMobilePunchDetailDTO userDetail(MobilePunchDetailQuery query) {
        // 1. 获取用户相关信息
        AttendanceUser attendanceUser = userService.getByUserId(query.getUserId());
        if (Objects.isNull(attendanceUser)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        // 获取用户的考勤规则
        UserMobileRuleConfigDTO userMobileRuleConfigDTO = getUserMobileRuleConfigDTO(attendanceUser.getId(), query.getDateTime());

        // 2. 获取日期相关参数
        Date dateTime = query.getDateTime();
        Long dayId = DateHelper.getDayId(dateTime);
        //获取当前时间-1day,+1day的班次配置
        Long preDayId = DateHelper.getDayId(DateUtil.offsetDay(dateTime, -1));
        Long nextDayId = DateHelper.getDayId(DateUtil.offsetDay(dateTime, 1));
        List<Long> dayIdRange = Arrays.asList(preDayId, dayId, nextDayId);

        // 3. 获取用户排班记录,获取用户-1day,+1day的排班记录
        List<UserShiftConfigDO> userShiftConfigDOList =
                userShiftConfigManage.selectUserShift(query.getUserId(), preDayId, nextDayId);

        // 4. 没有排班记录，返回异常打卡详情
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            //三天都没有排班，返回未排班异常
            return buildNoShiftAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        }

        // 5. 排班记录按日期分组
        Map<Long, UserShiftConfigDO> userShiftConfigMap = userShiftConfigDOList.stream()
                .collect(Collectors.toMap(UserShiftConfigDO::getDayId, Function.identity(), (oldVal, newVal) -> oldVal));

        // 6. 筛选有班次ID的排班记录
        List<UserShiftConfigDO> hasPunchClassShiftConfigList = userShiftConfigDOList.stream()
                .filter(UserShiftConfigDO::havePunchClassId)
                .collect(Collectors.toList());

        // 7. 处理没有有效班次的情况（休息日或法定节假日）
        if (CollectionUtils.isEmpty(hasPunchClassShiftConfigList)) {
            return handleEmptyPunchClassConfig(attendanceUser, dayId, userMobileRuleConfigDTO);
        }

        // 8. 获取班次配置，找到对应的考勤日
        List<Long> userPunchClassIdList = hasPunchClassShiftConfigList.stream()
                .map(UserShiftConfigDO::getPunchClassConfigId)
                .distinct()
                .collect(Collectors.toList());
        List<PunchClassItemConfigDO> classItemConfigDOList =
                punchClassConfigManage.selectLatestByClassIds(userPunchClassIdList);

        // 9. 获取考勤日和班次ID
        AttendanceDayClassInfoDTO attendanceDayClassInfo = this.getAttendanceDayAndClassId(
                dateTime,
                dayIdRange,
                userMobileRuleConfigDTO,
                classItemConfigDOList,
                userShiftConfigMap
        );

        // 10. 未找到考勤日，处理特殊情况
        if (attendanceDayClassInfo.isEmpty()) {
            return handleEmptyAttendanceDay(attendanceUser, dayId, userMobileRuleConfigDTO, userShiftConfigMap);
        }

        // 11. 通过考勤日找到对应的排班、班次、打卡规则
        return buildDetailForValidAttendanceDay(attendanceUser, dateTime, dayId, dayIdRange,
                userMobileRuleConfigDTO, classItemConfigDOList, attendanceDayClassInfo);
    }

    /**
     * 个人指定天的考勤详情
     */
    public UserDayMobilePunchDetailDTO userDetailByDayId(MobileDayPunchDetailQuery query) {
        AttendanceUser attendanceUser = userService.getByUserId(query.getUserId());
        if (Objects.isNull(attendanceUser)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        // 获取用户的考勤规则
        UserMobileRuleConfigDTO userMobileRuleConfigDTO = getUserMobileRuleConfigDTO(attendanceUser.getId(), query.getDateTime());

        Long dayId = query.getDayId();
        Date dateTime = query.getDateTime();

        List<UserShiftConfigDO> userShiftConfigs =
                userShiftConfigManage.selectUserShiftByDayIds(query.getUserId(), Collections.singletonList(dayId));
        if (CollectionUtils.isEmpty(userShiftConfigs)) {
            // 处理没有排班的情况
            return handleNoShiftConfig(attendanceUser, dayId, userMobileRuleConfigDTO, query.getUserId());
        }

        // 判断是否免打卡
        UserDayMobilePunchDetailDTO mobilePunchDetailDTO = buildNoPunchCardAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        if (mobilePunchDetailDTO != null) {
            return mobilePunchDetailDTO;
        }
        UserShiftConfigDO userShiftConfigDO = userShiftConfigs.get(0);

        // 有排班，但是排班是休息日，则返回用户打卡记录 和 休息日类型
        if (!userShiftConfigDO.havePunchClassId()) {
            // 构建休息日类型的异常打卡详情
            return buildAbnormalPunchDetailByType(
                    attendanceUser,
                    dayId,
                    userMobileRuleConfigDTO,
                    AbnormalPunchStatusEnum.REST.getCode(),
                    true
            );
        }
        //获取当前时间的打卡班次及详情
        Long punchClassId = userShiftConfigDO.getPunchClassConfigId();
        PunchClassConfigDO punchClassConfig = punchClassConfigDao.getById(punchClassId);
        List<PunchClassItemConfigDO> classItemConfigDOList =
                punchClassConfigManage.selectLatestByClassIds(Collections.singletonList(punchClassId));
        //通过排班查询对应的异常/正常记录
        return buildDetailDTO(
                attendanceUser,
                userMobileRuleConfigDTO,
                punchClassConfig,
                classItemConfigDOList,
                dayId,
                dayId,
                Collections.singletonList(dayId),
                dateTime,
                punchClassId
        );
    }


    /**
     * 处理没有排班的情况
     */
    private UserDayMobilePunchDetailDTO handleNoShiftConfig(AttendanceUser attendanceUser,
                                                            Long dayId,
                                                            UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                            Long userId) {
        //没有排班，按照当前日期查询日历，看是否是休息日
        List<CalendarConfigRangeDO> userCalendarRanges = calendarManage.selectCalendarConfigRange(
                Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userCalendarRanges)) {
            int year = DateHelper.getYearFormDayId(dayId);
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CALENDAR_NOT_EXIST, year, year);
        }
        CalendarConfigRangeDO calendarConfigRangeDO = userCalendarRanges.get(0);
        Long attendanceConfigId = calendarConfigRangeDO.getAttendanceConfigId();
        CalendarConfigDetailDO calendarConfigDetailDO = userCalendarService.getCalendarDetailByConfigIdAndDayId(attendanceConfigId, dayId);
        if (Objects.isNull(calendarConfigDetailDO)) {
            // 没有排班,也不是休息日，则返回用户打卡记录和未排班 类型
            return buildNoShiftAbnormalPunchDetail(
                    attendanceUser,
                    dayId,
                    userMobileRuleConfigDTO);
        }
        // 没有排班,有休息日，返回用户打卡记录和未排班-休息日 类型
        return buildAbnormalPunchDetailByType(
                attendanceUser,
                dayId,
                userMobileRuleConfigDTO,
                AbnormalPunchStatusEnum.N0_CLASS_REST.getCode(),
                true
        );
    }

    /**
     * 处理当前用户的请假/外勤/补卡 流程
     */
    public void dealAttendanceFormList(UserDayMobilePunchDetailDTO detailDTO) {
        ApplicationFormQuery formQuery = ApplicationFormQuery.builder()
                .userId(detailDTO.getUserId())
                .fromTypeList(FormTypeEnum.getAttendanceCodeList())
                .statusList(FormTypeEnum.getAttendanceCodeList())
                .build();
        List<AttendanceFormDO> formDOList = attendanceFormManage.selectForm(formQuery);
        if (CollectionUtils.isEmpty(formDOList)) {
            return;
        }
        List<Long> formIdList = formDOList.stream()
                .map(AttendanceFormDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> formAttrDOListAll = attendanceFormManage.selectFormAttrByFormIdLit(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> passFormAttrMap = formAttrDOListAll.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        List<MobileFormDTO> effectFormList = new ArrayList<>();
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> formAttrDOList = passFormAttrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            AttendanceFormAttrDO isRevoke = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.isRevoke);
            if (Objects.nonNull(isRevoke) && StringUtils.isNotBlank(isRevoke.getAttrValue()) &&
                    isRevoke.getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            AttendanceFormAttrDO leaveStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveStartDate);
            AttendanceFormAttrDO leaveEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveEndDate);
            AttendanceFormAttrDO reissueCardDayIdAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.reissueCardDayId);
            AttendanceFormAttrDO outOfOfficeStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
            AttendanceFormAttrDO outOfOfficeEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeEndDate);
            //查询请假时间是不是包含本次周期
            if (Objects.nonNull(leaveStartDateAttr) && Objects.nonNull(leaveEndDateAttr)) {
                Long leaveStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateAttr.getAttrValue()));
                Long leaveEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateAttr.getAttrValue()));
                if (leaveEndDayId.compareTo(detailDTO.getDayId()) < 0 || leaveStartDayId.compareTo(detailDTO.getDayId()) > 0) {
                    continue;
                }
                //结束时间不能为当天的最早时间
                Date attendanceDate = DateHelper.transferDayIdToDate(detailDTO.getDayId());
                DateTime beginOfDay = DateUtil.beginOfDay(attendanceDate);
                if (DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateAttr.getAttrValue()).compareTo(beginOfDay) == 0) {
                    continue;
                }
                MobileFormDTO mobileFormDTO = BeanUtils.convert(formDO, MobileFormDTO.class);
                mobileFormDTO.setLeaveStartDate(leaveStartDateAttr.getAttrValue());
                mobileFormDTO.setLeaveEndDate(leaveEndDateAttr.getAttrValue());
                effectFormList.add(mobileFormDTO);
            }
            if (Objects.nonNull(outOfOfficeStartDateAttr) && Objects.nonNull(outOfOfficeEndDateAttr)) {
                Long outOfOfficeStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateAttr.getAttrValue()));
                Long outOfOfficeEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateAttr.getAttrValue()));
                if (outOfOfficeEndDayId.compareTo(detailDTO.getDayId()) < 0 || outOfOfficeStartDayId.compareTo(detailDTO.getDayId()) > 0) {
                    continue;
                }
                MobileFormDTO mobileFormDTO = BeanUtils.convert(formDO, MobileFormDTO.class);
                mobileFormDTO.setLeaveStartDate(outOfOfficeStartDateAttr.getAttrValue());
                mobileFormDTO.setLeaveEndDate(outOfOfficeEndDateAttr.getAttrValue());
                effectFormList.add(mobileFormDTO);
            }
            if (Objects.nonNull(reissueCardDayIdAttr)) {
                Long reissueCardDayId = Long.valueOf(reissueCardDayIdAttr.getAttrValue());
                if (detailDTO.getDayId().equals(reissueCardDayId)) {
                    MobileFormDTO mobileFormDTO = BeanUtils.convert(formDO, MobileFormDTO.class);
                    mobileFormDTO.setReissueCardDayId(reissueCardDayId);
                    effectFormList.add(mobileFormDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(effectFormList)) {
            detailDTO.setMobileFormDTOList(effectFormList);
        }
    }


    private UserDayMobilePunchDetailDTO buildDetailForValidAttendanceDay(AttendanceUser attendanceUser, Date dateTime,
                                                                         Long dayId, List<Long> dayIdRange,
                                                                         UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                                         List<PunchClassItemConfigDO> classItemConfigDOS,
                                                                         AttendanceDayClassInfoDTO attendanceDayClassInfo) {
        // 获取用户的考勤规则
        UserDayMobilePunchDetailDTO mobilePunchDetailDTO = buildNoPunchCardAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        if (Objects.nonNull(mobilePunchDetailDTO)) {
            return mobilePunchDetailDTO;
        }
        // 获取考勤日ID
        Long attendanceDayId = attendanceDayClassInfo.getAttendanceDayId();

        // 获取打卡班次及详情
        Long punchClassId = attendanceDayClassInfo.getPunchClassId();
        PunchClassConfigDO punchClassConfig = punchClassConfigDao.getById(punchClassId);
        List<PunchClassItemConfigDO> punchClassItemConfig = classItemConfigDOS.stream()
                .filter(item -> item.getPunchClassId().equals(punchClassId))
                .collect(Collectors.toList());

        // 构建详情DTO
        return buildDetailDTO(
                attendanceUser,
                userMobileRuleConfigDTO,
                punchClassConfig,
                punchClassItemConfig,
                attendanceDayId,
                dayId,
                dayIdRange,
                dateTime,
                punchClassId
        );
    }

    private UserDayMobilePunchDetailDTO buildDetailDTO(AttendanceUser attendanceUser,
                                                       UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                       PunchClassConfigDO punchClassConfigDO,
                                                       List<PunchClassItemConfigDO> punchClassItemConfigList,
                                                       Long attendanceDayId,
                                                       Long dayId,
                                                       List<Long> dayIdList,
                                                       Date dateTime,
                                                       Long punchClassId) {
        // 转换班次规则
        PunchClassConfigDTO punchClassConfigDTO = MobileConfigMapstruct.INSTANCE.toPunchClassConfigDTO(punchClassConfigDO);
        // 转换班次时段时间为具体的年月日+时分秒
        punchClassConfigQueryService.transferItemConfigTimeFormat(punchClassItemConfigList, Optional.ofNullable(attendanceDayId).orElse(dayId));
        // 转转班次时间配置
        List<PunchClassItemConfigDTO> punchClassItemConfigDTOList =
                PunchClassConfigApiMapstruct.INSTANCE.toPunchClassItemConfigDTO(punchClassItemConfigList);

        UserDayMobilePunchDetailDTO mobilePunchDetailDTO = UserDayMobilePunchDetailDTO.builder()
                .userId(attendanceUser.getId())
                .locationCountry(attendanceUser.getLocationCountry())
                .dayId(Optional.ofNullable(attendanceDayId).orElse(dayId))
                .ruleConfigDTO(userMobileRuleConfigDTO)
                .punchClassConfigDTO(punchClassConfigDTO)
                .punchClassItemConfigDTO(punchClassItemConfigDTOList)
                .build();

        //查询异常记录，去除没有排班的异常记录
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectAbnormalByUserIdAndDayId(
                        attendanceUser.getId(), Optional.ofNullable(attendanceDayId).orElse(dayId))
                .stream()
                .filter(item -> (!Objects.equals(item.getAbnormalType(), AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode())
                        && !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus())))
                .collect(Collectors.toList());

        // 判断当前异常时间是否大于每个班次时段的最晚下班时间+3小时，大于才展示下班异常信息
        abnormalAttendanceDOList = employeeAbnormalAttendanceService.filterAbnormalAttendance(abnormalAttendanceDOList, punchClassItemConfigList, dateTime);
        if (CollectionUtils.isNotEmpty(abnormalAttendanceDOList)) {
            // 考勤异常，返回用户打卡记录及考勤类型
            List<MobilePunchCardRecordDTO> punchCardRecordDTOS = getPunchCardRecordDTOByClass(
                    attendanceUser.getUserCode(),
                    dayIdList,
                    punchClassId,
                    punchClassItemConfigDTOList
            );
            mobilePunchDetailDTO.setAbnormalPunchList(BeanUtils.convert(MobileAbnormalDTO.class, abnormalAttendanceDOList));
            mobilePunchDetailDTO.setPunchCardRecordDTO(punchCardRecordDTOS);
            mobilePunchDetailDTO.setDayId(Optional.ofNullable(attendanceDayId).orElse(dayId));
            return mobilePunchDetailDTO;
        }
        //不存在异常数据，有可能没有打卡或者正常考勤
        //查询当天正常考勤表的数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeList = attendanceEmployeeDetailManage.selectByDayId(attendanceUser.getId(), attendanceDayId);
        List<MobilePunchCardRecordDTO> punchCardRecordDTOList = getPunchCardRecordDTOByClass(
                attendanceUser.getUserCode(),
                dayIdList,
                punchClassId,
                punchClassItemConfigDTOList
        );
        if (CollectionUtils.isEmpty(attendanceEmployeeList)) {
            //正常异常考勤记录都不存在，查询打卡记录返回即可，不显示考勤异常
            mobilePunchDetailDTO.setPunchCardRecordDTO(punchCardRecordDTOList);
            mobilePunchDetailDTO.setDayId(Optional.ofNullable(attendanceDayId).orElse(dayId));
            return mobilePunchDetailDTO;
        }
        //用户当天有正常考勤且有请假/外出，返回对应的请假/外出时间及单据信息
        List<String> concreteTypeList = attendanceEmployeeList.stream()
                .map(AttendanceEmployeeDetailDO::getConcreteType)
                .collect(Collectors.toList());
        if (concreteTypeList.contains(AttendanceConcreteTypeEnum.P.getCode()) ||
                judgeAttendanceNormalDetail(attendanceEmployeeList, punchClassConfigDO)) {
            List<MobileFormDTO> attendanceFormDTOList = buildAttendanceForm(attendanceEmployeeList);
            if (CollectionUtils.isNotEmpty(attendanceFormDTOList)) {
                mobilePunchDetailDTO.setMobileFormDTOList(attendanceFormDTOList);
            }
        }
        mobilePunchDetailDTO.setPunchCardRecordDTO(punchCardRecordDTOList);
        return mobilePunchDetailDTO;
    }

    /**
     * 判断当前用户当前考勤日是否有正常考勤
     */
    private Boolean judgeAttendanceNormalDetail(List<AttendanceEmployeeDetailDO> attendanceEmployeeList,
                                                PunchClassConfigDO dayClassConfigDO) {
        BigDecimal attendanceMinutes = BigDecimal.ZERO;
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        //先看该用户当天正常考勤表的数据是不是正常，统计正常出勤时长以及请假时长
        for (AttendanceEmployeeDetailDO detailDO : attendanceEmployeeList) {
            if (detailDO.getAttendanceMinutes() != null &&
                    detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
            }
            if (detailDO.getLeaveMinutes() != null &&
                    detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
            }
        }

        //获取当天的出勤时间
        BigDecimal defaultLegalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        List<BigDecimal> defaultAbnormalHours = attendanceEmployeeList.stream()
                .filter(item -> item.getLegalWorkingHours() != null &&
                        item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0 &&
                        item.getAttendanceMinutes() != null &&
                        item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0)
                .map(AttendanceEmployeeDetailDO::getLegalWorkingHours)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defaultAbnormalHours)) {
            defaultLegalWorkingHours = defaultAbnormalHours.get(0);
        }

        if (dayClassConfigDO.getLegalWorkingHours() != null &&
                dayClassConfigDO.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
            defaultLegalWorkingHours = dayClassConfigDO.getLegalWorkingHours();
        }
        BigDecimal defaultLegalWorkingMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);

        if (leaveMinutes.add(attendanceMinutes).compareTo(defaultLegalWorkingMinutes) >= 0) {
            return true;
        }

        return false;
    }

    /**
     * 返回正常考勤对应的请假/外勤记录
     */
    private List<MobileFormDTO> buildAttendanceForm(List<AttendanceEmployeeDetailDO> attendanceEmployeeList) {
        List<Long> formId = attendanceEmployeeList.stream()
                .map(AttendanceEmployeeDetailDO::getFormId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<MobileFormDTO> attendanceFormDTOList = null;
        if (CollectionUtils.isNotEmpty(formId)) {
            List<AttendanceFormDO> attendanceFormDOList = attendanceFormManage.selectByIdList(formId);
            if (CollectionUtils.isNotEmpty(attendanceFormDOList)) {
                attendanceFormDTOList = BeanUtils.convert(MobileFormDTO.class, attendanceFormDOList);
                for (MobileFormDTO attendanceMobileFormDTO : attendanceFormDTOList) {
                    List<AttendanceFormAttrDO> formAttrDOList = attendanceFormManage.selectFormAttrByFormId(attendanceMobileFormDTO.getId());
                    if (CollectionUtils.isNotEmpty(formAttrDOList)) {
                        AttendanceFormAttrDO leaveStartDate = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveStartDate);
                        if (leaveStartDate != null) {
                            attendanceMobileFormDTO.setLeaveStartDate(leaveStartDate.getAttrValue());
                        }

                        AttendanceFormAttrDO leaveEndDate = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveEndDate);
                        if (leaveEndDate != null) {
                            attendanceMobileFormDTO.setLeaveEndDate(leaveEndDate.getAttrValue());
                        }

                        AttendanceFormAttrDO outOfOfficeStartDate = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
                        if (outOfOfficeStartDate != null) {
                            attendanceMobileFormDTO.setOutOfOfficeStartDate(outOfOfficeStartDate.getAttrValue());
                        }

                        AttendanceFormAttrDO outOfOfficeEndDate = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeEndDate);
                        if (outOfOfficeEndDate != null) {
                            attendanceMobileFormDTO.setOutOfOfficeEndDate(outOfOfficeEndDate.getAttrValue());
                        }
                    }
                }
            }
        }
        return attendanceFormDTOList;
    }


    /**
     * 获取用户对应班次时段下的打卡记录
     */
    private List<MobilePunchCardRecordDTO> getPunchCardRecordDTOByClass(String userCode,
                                                                        List<Long> dayIds,
                                                                        Long punchClassId,
                                                                        List<PunchClassItemConfigDTO> classItemConfigDTOS) {
        // 获取用户打卡记录
        List<EmployeePunchRecordDO> userPunchRecords = punchRecordManage.getUserPunchRecords(userCode, dayIds);
        if (CollectionUtils.isEmpty(userPunchRecords)) {
            return Collections.emptyList();
        }

        // 筛选指定班次的配置项
        List<PunchClassItemConfigDTO> targetClassConfigs = classItemConfigDTOS.stream()
                .filter(item -> punchClassId.equals(item.getPunchClassId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(targetClassConfigs)) {
            return Collections.emptyList();
        }

        Set<Long> processedRecordIds = new HashSet<>();

        return targetClassConfigs.stream()
                .flatMap(classItemConfigDaoConfig -> userPunchRecords.stream()
                        .filter(punchRecord -> isRecordMatchTimeRange(punchRecord, classItemConfigDaoConfig))
                        .filter(punchRecord -> processedRecordIds.add(punchRecord.getId()))
                        .map(punchRecord ->
                                MobileConfigMapstruct.INSTANCE.toMobilePunchCardRecordDTO(punchRecord, classItemConfigDaoConfig)))
                .collect(Collectors.toList());
    }

    /**
     * 检查打卡记录是否在指定时间范围内
     */
    private boolean isRecordMatchTimeRange(EmployeePunchRecordDO punchRecord,
                                           PunchClassItemConfigDTO classItemConfigDTO) {
        if (punchRecord.getPunchTime() == null ||
                classItemConfigDTO.getEarliestPunchInTime() == null ||
                classItemConfigDTO.getLatestPunchOutTime() == null) {
            return false;
        }

        Date normalizedPunchTime = DateHelper.truncateToMinute(punchRecord.getPunchTime());
        return normalizedPunchTime.compareTo(classItemConfigDTO.getEarliestPunchInTime()) >= 0 &&
                normalizedPunchTime.compareTo(classItemConfigDTO.getLatestPunchOutTime()) <= 0;
    }



    private UserDayMobilePunchDetailDTO handleEmptyAttendanceDay(AttendanceUser attendanceUser,
                                                                 Long dayId,
                                                                 UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                                 Map<Long, UserShiftConfigDO> userShiftConfigMap) {
        // 当天没有排班和打卡规则
        UserShiftConfigDO userShiftConfigDO = userShiftConfigMap.get(dayId);
        if (StringUtils.isEmpty(userMobileRuleConfigDTO.getPunchConfigNo()) || Objects.isNull(userShiftConfigDO)) {
            return buildNoShiftAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        }

        // 判断是否为免打卡
        UserDayMobilePunchDetailDTO noPunchCardAbnormalPunchDetail = buildNoPunchCardAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        if (Objects.nonNull(noPunchCardAbnormalPunchDetail)) {
            return noPunchCardAbnormalPunchDetail;
        }

        // 判断休息日类型并构建响应
        if (Objects.isNull(userShiftConfigDO.getPunchClassConfigId())) {
            return buildAbnormalPunchDetailByType(
                    attendanceUser,
                    dayId,
                    userMobileRuleConfigDTO,
                    AbnormalPunchStatusEnum.REST.getCode(),
                    true
            );
        } else {
            return buildNoShiftAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        }
    }

    /**
     * 获取考勤日和班次ID信息
     *
     * @param dateTime 当前日期时间
     * @param dayIds 日期ID列表（前一天、当天、后一天）
     * @param userMobileRuleConfigDTO 用户移动打卡规则配置
     * @param classItemConfigDOList 班次时段配置列表
     * @param userShiftConfigMap 用户排班配置映射
     * @return 考勤日和班次信息DTO
     */
    private AttendanceDayClassInfoDTO getAttendanceDayAndClassId(Date dateTime,
                                                                 List<Long> dayIds,
                                                                 UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                                 List<PunchClassItemConfigDO> classItemConfigDOList,
                                                                 Map<Long, UserShiftConfigDO> userShiftConfigMap) {
        AttendanceDayClassInfoDTO attendanceInfo = new AttendanceDayClassInfoDTO();
        //上班时间为空的可能为自由排班
        List<PunchClassItemConfigDO> freeWorkClassItemConfigDOS = classItemConfigDOList.stream()
                .filter(item -> Objects.isNull(item.getPunchInTime()))
                .collect(Collectors.toList());
        classItemConfigDOList = classItemConfigDOList.stream()
                .filter(item -> Objects.nonNull(item.getPunchInTime()))
                .collect(Collectors.toList());
        String punchConfigType = userMobileRuleConfigDTO.getPunchConfigType();
        boolean isFreeWork = Objects.equals(punchConfigType, PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode());
        for (PunchClassItemConfigDO punchClassItemConfigDO : classItemConfigDOList) {
            for (Long dayId : dayIds) {
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(
                        dayId,
                        punchClassItemConfigDO.getId(),
                        classItemConfigDOList
                );
                if (Objects.isNull(dayPunchTimeDTO)) {
                    continue;
                }
                if (dateTime.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0 &&
                        dateTime.compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0) {
                    //查出来的班次和排班记录对不上，则不是当前对应的考勤日
                    UserShiftConfigDO userDayShiftConfig = userShiftConfigMap.get(dayId);
                    if (Objects.isNull(userDayShiftConfig) ||
                            !punchClassItemConfigDO.getPunchClassId().equals(userDayShiftConfig.getPunchClassConfigId())) {
                        continue;
                    }
                    //在最早最晚打卡时间范围内，则是对应的考勤日
                    attendanceInfo.setAttendanceDayId(dayId);
                    attendanceInfo.setPunchClassId(punchClassItemConfigDO.getPunchClassId());
                    attendanceInfo.setPunchClassItemId(punchClassItemConfigDO.getId());
                    return attendanceInfo;
                }
            }
        }
        // 固班,班次查不到 查看是否为自由排班
        if (CollectionUtils.isEmpty(freeWorkClassItemConfigDOS)) {
            return attendanceInfo;
        }
        for (PunchClassItemConfigDO itemConfigDO : freeWorkClassItemConfigDOS) {
            for (Long dayId : dayIds) {
                UserShiftConfigDO userDayShiftConfig = userShiftConfigMap.get(dayId);
                if (Objects.isNull(userDayShiftConfig)) {
                    continue;
                }
                //判断是否是自由排班 自由排班只需要取时段最早和最晚打卡时间，判断是否在班次时间范围内即可
                if (!isFreeWork) {
                    continue;
                }
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.
                        getUserFreeWorkPunchClassItemDayTime(dayId, itemConfigDO);
                if (dateTime.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0 &&
                        dateTime.compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0) {
                    //查出来的班次和排班记录对不上，则不是当前对应的考勤日
                    if (!itemConfigDO.getPunchClassId().equals(userDayShiftConfig.getPunchClassConfigId())) {
                        continue;
                    }
                    //在最早最晚打卡时间范围内，则是对应的考勤日
                    attendanceInfo.setAttendanceDayId(dayId);
                    attendanceInfo.setPunchClassId(itemConfigDO.getPunchClassId());
                    attendanceInfo.setPunchClassItemId(itemConfigDO.getId());
                    return attendanceInfo;
                }
            }
        }
        return attendanceInfo;
    }

    private UserDayMobilePunchDetailDTO handleEmptyPunchClassConfig(AttendanceUser attendanceUser,
                                                                    Long dayId,
                                                                    UserMobileRuleConfigDTO userMobileRuleConfigDTO) {
        // 判断用户是否为免打卡
        UserDayMobilePunchDetailDTO noPunchCardAbnormalPunchDetail = buildNoPunchCardAbnormalPunchDetail(attendanceUser, dayId, userMobileRuleConfigDTO);
        if (Objects.nonNull(noPunchCardAbnormalPunchDetail)) {
            return noPunchCardAbnormalPunchDetail;
        }
        // 构建休息日类型的异常打卡详情
        return buildAbnormalPunchDetailByType(
                attendanceUser,
                dayId,
                userMobileRuleConfigDTO,
                AbnormalPunchStatusEnum.REST.getCode(),
                true
        );
    }


    /**
     * 构建未排班异常
     */
    private UserDayMobilePunchDetailDTO buildNoShiftAbnormalPunchDetail(AttendanceUser attendanceUser,
                                                                        Long dayId,
                                                                        UserMobileRuleConfigDTO userMobileRuleConfigDTO) {
        return buildAbnormalPunchDetailByType(
                attendanceUser,
                dayId,
                userMobileRuleConfigDTO,
                AbnormalPunchStatusEnum.N0_CLASS.getCode(),
                true
        );
    }

    /**
     * 构建免打卡异常
     */
    private UserDayMobilePunchDetailDTO buildNoPunchCardAbnormalPunchDetail(AttendanceUser attendanceUser,
                                                                            Long dayId,
                                                                            UserMobileRuleConfigDTO userMobileRuleConfigDTO) {
        if (StringUtils.isNotBlank(userMobileRuleConfigDTO.getPunchConfigNo())) {
            String punchConfigType = userMobileRuleConfigDTO.getPunchConfigType();
            if (Objects.equals(punchConfigType, PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode())) {
                return buildAbnormalPunchDetailByType(
                        attendanceUser,
                        dayId,
                        userMobileRuleConfigDTO,
                        AbnormalPunchStatusEnum.NO_PUNCH_CARD.getCode(),
                        false
                );
            }
        }
        return null;
    }

    /**
     * 根据异常类型构建异常打卡详情
     */
    private UserDayMobilePunchDetailDTO buildAbnormalPunchDetailByType(AttendanceUser attendanceUser,
                                                                       Long dayId,
                                                                       UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                                       String abnormalType,
                                                                       boolean retrievePunchRecords) {
        // 获取用户打卡记录（如果需要）
        List<EmployeePunchRecordDO> userPunchRecords = null;
        if (retrievePunchRecords) {
            userPunchRecords = punchRecordManage.getUserPunchRecords(
                    attendanceUser.getUserCode(), Collections.singletonList(dayId)
            );
        }

        // 调用构建异常返回类型方法
        return buildAbnormalPunchDTO(
                attendanceUser.getId(),
                attendanceUser.getLocationCountry(),
                abnormalType,
                userMobileRuleConfigDTO,
                userPunchRecords,
                dayId
        );
    }

    /**
     * 构建异常返回类型
     */
    private UserDayMobilePunchDetailDTO buildAbnormalPunchDTO(Long userId,
                                                              String locationCountry,
                                                              String abnormalType,
                                                              UserMobileRuleConfigDTO userMobileRuleConfigDTO,
                                                              List<EmployeePunchRecordDO> userPunchRecordDOS,
                                                              Long dayId) {
        MobileAbnormalDTO abnormalPunchDTO = MobileAbnormalDTO.builder()
                .abnormalType(abnormalType)
                .build();
        UserDayMobilePunchDetailDTO punchDetailDTO = UserDayMobilePunchDetailDTO.builder()
                .userId(userId)
                .locationCountry(locationCountry)
                .dayId(dayId)
                .abnormalPunchList(Collections.singletonList(abnormalPunchDTO))
                .build();

        // 添加打卡记录
        if (CollectionUtils.isNotEmpty(userPunchRecordDOS)) {
            List<MobilePunchCardRecordDTO> userPunchRecords =
                    MobileConfigMapstruct.INSTANCE.toMobilePunchCardRecordDTO(userPunchRecordDOS);
            punchDetailDTO.setPunchCardRecordDTO(userPunchRecords);
        }
        // 添加考勤规则配置
        if (null != userMobileRuleConfigDTO) {
            punchDetailDTO.setRuleConfigDTO(userMobileRuleConfigDTO);
        }
        return punchDetailDTO;
    }

    /**
     * 获取用户打卡规则和补卡规则
     */
    public UserMobileRuleConfigDTO getUserMobileRuleConfigDTO(Long userId, Date dateTime) {
        List<Long> userList = Collections.singletonList(userId);
        //打卡规则
        Map<Long, PunchConfigDO> userPunchConfigMap = punchConfigManage.mapByUserIds(userList, dateTime);
        PunchConfigDO userPunchConfigDO = userPunchConfigMap.get(userId);
        //补卡规则
        Map<Long, ReissueCardConfigDO> reissueCardConfigMap = reissueCardConfigManage.mapByUserIds(userList, dateTime);
        ReissueCardConfigDO userReissueCardConfigDO = reissueCardConfigMap.get(userId);
        return MobileConfigMapstruct.INSTANCE.toRuleConfigDTO(userPunchConfigDO, userReissueCardConfigDO);
    }
}
