package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.bizMq.sync.SyncHrToAttendanceTableService;
import com.imile.attendance.calendar.CalendarConfigRangeService;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.rule.RuleConfigRangeService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.third.ThirdZktecoService;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.vacation.job.service.UserLeaveInspectionService;
import com.imile.attendance.vacation.param.UserLeaveInspectionParam;
import com.imile.hrms.api.base.result.OperatorDTO;
import com.imile.hrms.api.user.param.UserEventParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description 监听用户入职消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.user.entry.group}",
        selectorExpression = "${rocket.mq.hr.user.entry.tag}",
        consumeThreadMax = 4)
public class HrUserEntryListener extends BaseRocketMQListener {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Resource
    private SyncHrToAttendanceTableService syncHrToAttendanceTableService;
    @Resource
    private CalendarConfigRangeService calendarConfigRangeService;
    @Resource
    private RuleConfigRangeService ruleConfigRangeService;
    @Resource
    private ThirdZktecoService thirdZktecoService;
    @Resource
    private UserService userService;
    @Resource
    private PunchClassConfigApplicationService classConfigApplicationService;
    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private UserLeaveInspectionService userLeaveInspectionService;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;

    @Override
    public void doOnMessage(MessageExt messageExt) {
        UserEventParam<?> param = JSON.parseObject(new String(messageExt.getBody()), UserEventParam.class);
        log.info("收到人员入职消息,msgId:{},topic:{},tags:{},param:{}",
                messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
        OperatorHelper.putOperatorInfo(param.getOperator());
        String userCode = param.getUserCode();
        log.info("收到人员入职消息，userCode:{}", userCode);
        if (StringUtils.isEmpty(userCode)) {
            log.error("收到人员入职消息，userCode为空");
            return;
        }
        //查询hr人员信息(此时考勤还没有这个人)
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
        if (hrmsUserInfoDO == null) {
            log.error("hr人员信息表中不存在该人员，userCode:{}", userCode);
            return;
        }
        HrmsUserEntryRecordDO hrmsUserEntryRecordDO = hrmsUserEntryRecordDao.getByUserId(hrmsUserInfoDO.getId());
        if (hrmsUserEntryRecordDO == null) {
            log.error("userId:{} 员工在hr入职表不存在,不同步", userCode);
            return;
        }
        //1.新增或更新人员表和入职表数据
        syncHrToAttendanceTableService.syncUserEntryRecord(hrmsUserInfoDO);
        //2.新增人员考勤数据doBusinessAfterEntry
        syncUserToAttendance(
                CommonMapstruct.INSTANCE.hrMapToUser(hrmsUserInfoDO),
                CommonMapstruct.INSTANCE.hrMapToUserEntryRecord(hrmsUserEntryRecordDO)
        );
    }

    private void syncUserToAttendance(AttendanceUser attendanceUser,
                                      AttendanceUserEntryRecord attendanceUserEntryRecord) {
        // 设置个人假期
        userLeaveService.userEntryAddLeaveInfo(attendanceUser);

        if (!userService.checkValidUserAttendanceRange(attendanceUser.getId())) {
            log.info("该用户不在考勤有效用户范围中: {}", attendanceUser.getUserCode());
            return;
        }

        //处理考勤日历和考勤规则信息
        calendarConfigRangeService.addCalendarConfigRange(attendanceUser, attendanceUserEntryRecord);
        ruleConfigRangeService.addRuleConfigRange(attendanceUser, attendanceUserEntryRecord);

        //设置员工班次性质
        userService.setUserClassNature(attendanceUser.getUserCode(), attendanceUserEntryRecord);

        //固定班次自动排班
        classConfigApplicationService.userEntryAutoShift(attendanceUser.getUserCode(), attendanceUserEntryRecord);

        // 同步中控考勤机
        thirdZktecoService.syncEmployee2Zkteco(attendanceUser.getId());

        //发放员工假期
        Date startDate = getStartDate(attendanceUser);
        UserLeaveInspectionParam param = UserLeaveInspectionParam.builder()
                .userCodes(attendanceUser.getUserCode())
                .countryList(attendanceUser.getLocationCountry())
                .everyFullCurrentDate(DateHelper.formatYYYYMMDDHHMMSS(startDate))
                .build();
        userLeaveInspectionService.userLeaveInspectionHandler(param);
//
//        //HR确认入职延迟情况考勤侧处理
//        attendanceEntryAndDimissionService.entryAttendanceHandler(user.getId());
    }

    private Date getStartDate(AttendanceUser user) {
        Date startDate = new Date();
        //查询入职确认时间作为日历适用范围生效时间
        Optional<AttendanceUserEntryRecord> userEntryRecordOptional = userEntryRecordService.selectUserEntryByUserIds(Collections.singletonList(user.getId()))
                .stream()
                .filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus()))
                .findFirst();
        if (userEntryRecordOptional.isPresent()) {
            AttendanceUserEntryRecord userEntryRecord = userEntryRecordOptional.get();
            if (Objects.nonNull(userEntryRecord.getConfirmDate())) {
                startDate = userEntryRecord.getConfirmDate();
            }
        }
        return startDate;
    }


}
