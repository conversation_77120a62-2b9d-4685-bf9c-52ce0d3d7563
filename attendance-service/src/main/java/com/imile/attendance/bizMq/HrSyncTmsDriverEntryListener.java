package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.bizMq.sync.SyncHrToAttendanceTableService;
import com.imile.attendance.calendar.CalendarConfigRangeService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.user.UserLeaveService;
import com.imile.hrms.api.user.param.UserEventParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8 
 * @Description 监听hr同步tms司机入职的消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.driver.entry.group}",
        selectorExpression = "${rocket.mq.hr.driver.entry.tag}",
        consumeThreadMax = 4)
public class HrSyncTmsDriverEntryListener extends BaseRocketMQListener {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Resource
    private SyncHrToAttendanceTableService syncHrToAttendanceTableService;
    @Resource
    private CalendarConfigRangeService calendarConfigRangeService;
    @Resource
    private UserLeaveService userLeaveService;

    @Override
    public void doOnMessage(MessageExt messageExt) {
        UserEventParam<?> param = JSON.parseObject(new String(messageExt.getBody()), UserEventParam.class);
        log.info("收到hr同步tms司机入职消息,msgId:{},topic:{},tags:{},param:{}",
                messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
        String userCode = param.getUserCode();
        log.info("收到hr同步tms司机入职消息，userCode:{}", userCode);
        if (StringUtils.isEmpty(userCode)) {
            log.error("收到hr同步tms司机入职消息，userCode为空");
            return;
        }
        //查询hr人员信息(此时考勤还没有这个人)
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
        if (hrmsUserInfoDO == null) {
            log.error("hr人员信息表中不存在该人员，userCode:{}", userCode);
            return;
        }
        //todo 司机这时是否可以查询入职信息？
        HrmsUserEntryRecordDO hrmsUserEntryRecordDO = hrmsUserEntryRecordDao.getByUserId(hrmsUserInfoDO.getId());
        if (hrmsUserEntryRecordDO == null) {
            log.error("userId:{} 员工在hr入职表不存在", userCode);
            return;
        }
        //1.新增或更新人员表和入职表数据
        syncHrToAttendanceTableService.syncUserEntryRecord(hrmsUserInfoDO);
        //2.新增人员考勤数据
        syncTmsDriverToAttendance(
                CommonMapstruct.INSTANCE.hrMapToUser(hrmsUserInfoDO),
                CommonMapstruct.INSTANCE.hrMapToUserEntryRecord(hrmsUserEntryRecordDO)
        );
    }


    private void syncTmsDriverToAttendance(AttendanceUser attendanceUser,
                                           AttendanceUserEntryRecord attendanceUserEntryRecord) {
        // 新增司机设置假期
        userLeaveService.userEntryAddLeaveInfo(attendanceUser);
        // 处理考勤日历和考勤规则信息
        calendarConfigRangeService.addCalendarConfigRange(attendanceUser, attendanceUserEntryRecord);
//        punchConfigRangeService.addAttendancePunchConfigRange(userInfoDO);
    }
}
