package com.imile.attendance.user.impl;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.user.UserService;
import com.imile.attendance.user.vo.UserOptionVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    private final UserInfoDao userInfoDao;
    private final UserClassNatureModifyRecordDao userClassNatureModifyRecordDao;
    private final DefaultIdWorker defaultIdWorker;
    private final CountryService countryService;
    private final AttendanceDeptService attendanceDeptService;

    @Override
    public List<UserOptionVO> getUserAssociateList(UserDaoQuery userDaoQuery) {
        buildUserPermissionQuery(userDaoQuery);
        List<UserInfoDO> userInfoDOList = userInfoDao.selectByAssociateCondition(userDaoQuery);
        return userInfoDOList.stream()
                .map(s -> UserOptionVO.builder()
                        .id(s.getId())
                        .userCode(s.getUserCode())
                        .userName(getUnifiedUserName(s.getUserName(), s.getUserNameEn()))
                        .deptId(s.getDeptId())
                        .postId(s.getPostId())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void setUserClassNature(String userCode, AttendanceUserEntryRecord attendanceUserEntryRecord) {
        UserInfoDO userInfo = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfo)) {
            log.error("员工入职设置班次性质:用户查询为空");
            return;
        }
        List<UserInfoDO> deptUserInfoList = userInfoDao.userList(UserDaoQuery.builder().deptId(userInfo.getDeptId()).build());
        int fixedClassCount = 0;
        int multipleClassCount = 0;
        for (UserInfoDO userInfoDO : deptUserInfoList) {
            if (StringUtils.isEmpty(userInfoDO.getClassNature())) {
                continue;
            }
            if (Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), userInfoDO.getClassNature())) {
                fixedClassCount++;
            } else {
                multipleClassCount++;
            }
        }
        String classNature = ClassNatureEnum.FIXED_CLASS.name();
        if (multipleClassCount > fixedClassCount) {
            classNature = ClassNatureEnum.MULTIPLE_CLASS.name();
        }
        userInfo.setClassNature(classNature);
        BaseDOUtil.fillDOUpdateByUserOrSystem(userInfo);
        userInfoDao.updateById(userInfo);

        UserClassNatureModifyRecordDO userClassNatureModifyRecordDO = new UserClassNatureModifyRecordDO();
        userClassNatureModifyRecordDO.setId(defaultIdWorker.nextId());
        userClassNatureModifyRecordDO.setUserId(userInfo.getId());
        userClassNatureModifyRecordDO.setClassNature(classNature);
        userClassNatureModifyRecordDO.setEffectTime(attendanceUserEntryRecord.getConfirmDate());
        userClassNatureModifyRecordDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        userClassNatureModifyRecordDO.setIsLatest(BusinessConstant.Y);
        userClassNatureModifyRecordDO.setRemark("员工入职班次性质初始化");
        BaseDOUtil.fillDOInsertByUsrOrSystem(userClassNatureModifyRecordDO);
        userClassNatureModifyRecordDao.save(userClassNatureModifyRecordDO);
    }

    @Override
    public boolean checkValidUserAttendanceRange(Long userId) {
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfoDO)) {
            return false;
        }
        if (!(Objects.equals(StatusEnum.ACTIVE.getCode(), userInfoDO.getStatus())
                && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), userInfoDO.getWorkStatus())
                && Objects.equals(BusinessConstant.N, userInfoDO.getIsDriver()))) {
            return false;
        }

        String locationCountry = userInfoDO.getLocationCountry();
        String employeeType = userInfoDO.getEmployeeType();
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        }
    }

    @Override
    public boolean checkValidDimissionUserAttendanceRange(Long userId) {
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfoDO)) {
            return false;
        }

        String locationCountry = userInfoDO.getLocationCountry();
        String employeeType = userInfoDO.getEmployeeType();
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        }
    }

    public String getUnifiedUserName(String userName, String userNameEn) {
        if (StringUtils.isBlank(userName)) {
            return "";
        }
        // 若userName为全中文且userNameEn不为空 则将俩字段的值拼接在一起再返回
        if (isAllInChinese(userName) && StringUtils.isNotBlank(userNameEn)) {
            return StringUtils.capitalize(userNameEn.trim()) + userName.trim();
        }
        // 否则直接返回userName的值
        return userName.trim();
    }

    /**
     * 判断关键字是否全中文
     *
     * @param keyword 关键字
     * @return Boolean
     */
    public Boolean isAllInChinese(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Boolean.TRUE;
        }
        String keywordTrim = keyword.trim();
        Pattern pattern = Pattern.compile(BusinessConstant.CHINESE_PATTERN);
        Matcher mather = pattern.matcher(keywordTrim);
        int length = 0;
        while (mather.find()) {
            length++;
        }
        return length == keywordTrim.length() ? Boolean.TRUE : Boolean.FALSE;
    }

    private void buildUserPermissionQuery(UserDaoQuery userDaoQuery) {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (userContext.isSystem()) {
            //系统管理员
            handleSysAdminQuery(userDaoQuery);
            return;
        }

        //没有国家和部门权限
        if (CollectionUtils.isEmpty(userContext.getCountryList()) && CollectionUtils.isEmpty(userContext.getOrganizationIds())) {
            handleNoPermissionQuery(userDaoQuery);
            return;
        }

        Set<String> countrySet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(userContext.getCountryList())) {
            countrySet.addAll(userContext.getCountryList());
            userDaoQuery.setAuthLocationCountryList(userContext.getCountryList());
        }

        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            List<String> deptCountries = getDeptCountries(userContext.getOrganizationIds());
            if (CollectionUtils.isNotEmpty(deptCountries)) {
                countrySet.addAll(deptCountries);
            }
            userDaoQuery.setAuthDeptList(userContext.getOrganizationIds());
        }
        handleUserCountryPermissionQuery(userDaoQuery, countrySet);
    }


    private void handleSysAdminQuery(UserDaoQuery userDaoQuery) {
        userDaoQuery.setIsNeedQuerySpecialCountry(true);
        userDaoQuery.setIsSystem(true);
        List<CountryDTO> allCountries = countryService.listAllCountry();
        // 分离普通国家和特殊国家
        List<String> normalCountryList = allCountries.stream()
                .map(CountryDTO::getCountryShort)
                .filter(countryShort -> !CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(countryShort))
                .distinct()
                .collect(Collectors.toList());
        userDaoQuery.setNormalCountryList(normalCountryList);
        userDaoQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        userDaoQuery.setSpecialCountryList(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT);
        userDaoQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
    }

    private List<String> getDeptCountries(List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return attendanceDeptService.listByDeptIds(deptIds).stream().map(AttendanceDept::getCountry).distinct().collect(Collectors.toList());
    }

    private static void handleUserCountryPermissionQuery(UserDaoQuery userDaoQuery, Set<String> deptCountries) {
        boolean hasSpecialCountry = deptCountries.stream()
                .anyMatch(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains);

        if (hasSpecialCountry) {
            // 特殊国家和普通国家分组
            Map<Boolean, List<String>> countryMap = deptCountries.stream()
                    .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));

            userDaoQuery.setIsNeedQuerySpecialCountry(true);
            userDaoQuery.setSpecialCountryList(countryMap.get(true));
            userDaoQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            if (CollectionUtils.isNotEmpty(countryMap.get(false))) {
                userDaoQuery.setNormalCountryList(countryMap.get(false));
                userDaoQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
        } else {
            // 只有普通国家
            userDaoQuery.setIsNeedQuerySpecialCountry(false);
            userDaoQuery.setNormalCountryList(new ArrayList<>(deptCountries));
            userDaoQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        }
    }

    private static void handleNoPermissionQuery(UserDaoQuery userDaoQuery) {
        userDaoQuery.setIsNeedQuerySpecialCountry(false);
        userDaoQuery.setNormalCountryList(Collections.emptyList());
        userDaoQuery.setSpecialCountryList(Collections.emptyList());
        userDaoQuery.setNormalEmployeeTypeList(Collections.emptyList());
        userDaoQuery.setSpecialEmployeeTypeList(Collections.emptyList());
    }
}
