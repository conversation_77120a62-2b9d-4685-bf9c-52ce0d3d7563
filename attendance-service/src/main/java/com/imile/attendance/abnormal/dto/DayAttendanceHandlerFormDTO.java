package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-24
 * @version: 1.0
 */
@Data
public class DayAttendanceHandlerFormDTO {
    /**
     * 单据ID
     */
    private Long formId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 请假/外勤开始时间
     */
    private Date startTime;

    /**
     * 请假/外勤结束时间
     */
    private Date endTime;

    private Long configId;

    private String leaveType;

    private Long startDayId;

    private Long endDayId;
}
