package com.imile.attendance.rule.service;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.rule.dto.DayNormalPunchTimeDTO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.rule.dto.DayNormalPunchTimeDTO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.query.PunchClassConfigDetailQuery;
import com.imile.attendance.rule.query.PunchClassConfigListQuery;
import com.imile.attendance.rule.query.PunchClassConfigUserQuery;
import com.imile.attendance.rule.vo.PunchClassConfigDetailVO;
import com.imile.attendance.rule.vo.PunchClassConfigExportVO;
import com.imile.attendance.rule.vo.PunchClassConfigVO;
import com.imile.common.page.PaginationResult;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/18
 */
public interface PunchClassConfigQueryService {

    /**
     * 班次列表
     */
    PaginationResult<PunchClassConfigVO> list(PunchClassConfigListQuery query);

    /**
     * 班次导出
     *
     * @param query
     * @return
     */
    PaginationResult<PunchClassConfigExportVO> export(PunchClassConfigListQuery query);

    /**
     * 班次详情
     */
    PunchClassConfigDetailVO detail(PunchClassConfigDetailQuery query);

    /**
     * 班次适用范围应用人数
     */
    PaginationResult<RuleConfigUserInfoDTO> pagePunchClassConfigUserList(PunchClassConfigUserQuery query);

    /**
     * 根据班次性质查询所有最新的班次
     *
     * @param classNature 班次性质
     * @return 班次列表
     */
    List<PunchClassConfigSelectDTO> selectLatestByClassNature(String classNature);


    /**
     * 查询用户关联的所有最新班次
     * 固定班次取优先级最高的
     * 多班次取所有
     *
     * @param userIdList 用户ID集合
     * @return 最新班次列表
     */
    List<UserClassConfigDTO> selectUserClassConfigList(List<Long> userIdList);

    /**
     * 查询最新且启用的班次适用范围内的所有用户ID集合
     *
     * @param classNature 班次性质
     * @param classIds    班次ID集合
     * @return 用户ID集合
     */
    Set<Long> selectAllUserIds(String classNature, List<Long> classIds);

    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是H/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    DayPunchTimeDTO getUserPunchDayTime(Long dayId, List<PunchClassItemConfigDO> itemConfigDOList);

    /**
     * 获取用户指定天的打卡规则的最早开始最晚截止时间(改天必须是有班次，不能是H/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    DayPunchTimeDTO getUserPunchClassItemDayTime(Long dayId, Long classItemId,
                                                 List<PunchClassItemConfigDO> itemConfigDOList);

    /**
     * 获取用户指定天的打卡规则的正常上下班时间(改天必须是有班次，不能是H/OFF/AL，并且不能是自由打卡规则)
     * 适用于固定/班次打卡规则
     */
    DayNormalPunchTimeDTO getUserPunchClassNormalItemDayTime(Long dayId, Long classItemId,
                                                             List<PunchClassItemConfigDO> itemConfigDOList);


    /**
     * 获取用户自由打卡规则指定天的最早开始最晚截止时间
     */
    DayPunchTimeDTO getUserFreeWorkPunchClassItemDayTime(Long dayId, PunchClassItemConfigDO itemConfigDO);

    /**
     * 查询同时包含班次集合的用户集合
     * 最新且启用
     * 多班次场景调用
     *
     * @param classNature 班次性质
     * @param classIds    班次ID集合
     * @return 用户ID集合
     */
    Set<Long> selectMatchAllClassIdsUserIds(String classNature, List<Long> classIds);

    /**
     * 转换班次时段格式
     */
    void transferItemConfigTimeFormat(List<PunchClassItemConfigDO> itemConfigDOS, Long dayId);


}
