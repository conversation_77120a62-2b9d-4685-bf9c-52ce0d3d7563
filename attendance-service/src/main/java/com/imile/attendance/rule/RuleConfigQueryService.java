package com.imile.attendance.rule;

import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.rule.RuleConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.CountryReissueCardConfig;
import com.imile.attendance.rule.bo.OverTimeConfigBO;
import com.imile.attendance.rule.bo.PunchConfigBO;
import com.imile.attendance.rule.bo.ReissueCardConfigBO;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import com.imile.attendance.rule.query.RuleConfigSelectQuery;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.enums.ErrorCodeEnums;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * <AUTHOR> chen
 * @Date 2025/5/6
 * @Description 规则配置查询服务
 */
@Component
public class RuleConfigQueryService {

    @Resource
    private PunchConfigDao punchConfigDao;

    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;

    @Resource
    private OverTimeConfigDao overTimeConfigDao;

    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;

    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;

    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;

    @Resource
    private PunchConfigManage punchConfigManage;

    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;

    @Resource
    private OverTimeConfigManage overTimeConfigManage;

    @Resource
    private AttendanceUserService attendanceUserService;

    @Resource
    private CountryService countryService;

    @Resource
    private CalendarManage calendarManage;

    /**
     * 考勤档案列表下拉
     * 是否附加未配置规则由入参选择
     */
    public List<RuleConfigSelectDTO> queryRuleConfigSelect(RuleConfigSelectQuery query, boolean isAppendNotConfigRule) {
        // 规则类型不能为空
        String ruleType = query.getRuleType();
        if (StringUtils.isBlank(ruleType)) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleType is required");
        }
        RuleConfigTypeEnum ruleConfigTypeEnum = RuleConfigTypeEnum.getInstance(ruleType);
        if (ruleConfigTypeEnum == null) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleType is invalid");
        }
        List<RuleConfigSelectDTO> ruleConfigSelectDTOs = new ArrayList<>();
        switch (ruleConfigTypeEnum) {
            case PUNCH_CONFIG:
                PunchConfigQuery punchConfigQuery = new PunchConfigQuery();
                punchConfigQuery.setPunchConfigName(query.getRuleName());
                punchConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
                punchConfigQuery.setCountry(query.getCountry());
                List<PunchConfigDO> punchConfigDOs = punchConfigDao.listByQuery(punchConfigQuery);
                ruleConfigSelectDTOs = punchConfigDOs.stream().map(punchConfigDO -> {
                    return RuleConfigSelectDTO.buildRuleConfigSelectDTO(ruleType, punchConfigDO.getId(),
                            punchConfigDO.getConfigNo(),
                            punchConfigDO.getConfigName());
                }).collect(Collectors.toList());
                break;
            case REISSUE_CARD_CONFIG:
                ReissueCardConfigQuery reissueCardConfigQuery = new ReissueCardConfigQuery();
                reissueCardConfigQuery.setConfigName(query.getRuleName());
                reissueCardConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
                reissueCardConfigQuery.setCountry(query.getCountry());
                List<ReissueCardConfigDO> reissueCardConfigDOs = reissueCardConfigDao.listByQuery(reissueCardConfigQuery);
                ruleConfigSelectDTOs = reissueCardConfigDOs.stream().map(reissueCardConfigDO -> {
                    return RuleConfigSelectDTO.buildRuleConfigSelectDTO(ruleType, reissueCardConfigDO.getId(),
                            reissueCardConfigDO.getConfigNo(),
                            reissueCardConfigDO.getConfigName());
                }).collect(Collectors.toList());
                break;
            case OVERTIME_CONFIG:
                OverTimeConfigQuery overTimeConfigQuery = new OverTimeConfigQuery();
                overTimeConfigQuery.setConfigName(query.getRuleName());
                overTimeConfigQuery.setStatus(StatusEnum.ACTIVE.getCode());
                overTimeConfigQuery.setCountry(query.getCountry());
                List<OverTimeConfigDO> overTimeConfigDOs = overTimeConfigDao.listByQuery(overTimeConfigQuery);
                ruleConfigSelectDTOs = overTimeConfigDOs.stream().map(overTimeConfigDO -> {
                    return RuleConfigSelectDTO.buildRuleConfigSelectDTO(ruleType, overTimeConfigDO.getId(),
                            overTimeConfigDO.getConfigNo(),
                            overTimeConfigDO.getConfigName());
                }).collect(Collectors.toList());
                break;
            case CALENDAR_CONFIG:
                List<CalendarConfigDO> calendarConfigDOs = calendarManage.getActiveCalendarConfigByCountry(query.getCountry(), query.getRuleName());
                ruleConfigSelectDTOs = calendarConfigDOs.stream().map(calendarConfigDO -> {
                    return RuleConfigSelectDTO.buildRuleConfigSelectDTO(ruleType, calendarConfigDO.getId(),
                            calendarConfigDO.getAttendanceConfigNo(),
                            calendarConfigDO.getAttendanceConfigName());
                }).collect(Collectors.toList());
                break;
            default:
                throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleType is invalid");
        }
        // 添加未配置规则
        if (isAppendNotConfigRule) {
            ruleConfigSelectDTOs.add(RuleConfigSelectDTO.buildNotConfigRuleConfigSelectDTO(ruleType));
        }
        return ruleConfigSelectDTOs;
    }


    /**
     * 根据规则配置下拉选择DTO查询用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 用户ID列表
     */
    public List<Long> queryRuleConfigUsers(RuleConfigSelectDTO ruleConfigSelectDTO) {
        switch (Objects.requireNonNull(RuleConfigTypeEnum.getInstance(ruleConfigSelectDTO.getRuleType()))) {
            case PUNCH_CONFIG:
                return getPunchConfigUsersByRuleSelectDTO(ruleConfigSelectDTO);
            case REISSUE_CARD_CONFIG:
                return getReissueCardConfigUsersByRuleSelectDTO(ruleConfigSelectDTO);
            case OVERTIME_CONFIG:
                return getOverTimeConfigUsersByRuleSelectDTO(ruleConfigSelectDTO);
            case CALENDAR_CONFIG:
                return getCalendarConfigUsersByRuleSelectDTO(ruleConfigSelectDTO);
            default:
                throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleType is invalid");
        }
    }

    /**
     * 根据打卡规则配置下拉选择DTO查询用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 用户ID列表
     */
    private List<Long> getPunchConfigUsersByRuleSelectDTO(RuleConfigSelectDTO ruleConfigSelectDTO) {
        if (StringUtils.isBlank(ruleConfigSelectDTO.getRuleName())) {
            return Collections.emptyList();
        }
        // 查询所有未配置规则的用户
        if (RuleConfigSelectDTO.isNotConfigRule(ruleConfigSelectDTO.getRuleName())) {
            List<CountryDTO> allCountries = countryService.listAllCountry();
            // 分离普通国家和特殊国家
            List<String> normalCountryList = allCountries.stream()
                    .map(CountryDTO::getCountryShort)
                    .filter(countryShort -> !CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(countryShort))
                    .distinct()
                    .collect(Collectors.toList());

            RuleRangeUserQuery ruleRangeUserQuery = new RuleRangeUserQuery();
            // 设置查询参数
            ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
            ruleRangeUserQuery.setSpecialCountryList(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalCountryList(normalCountryList);

            // 处理用工类型
            ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);

            List<UserInfoDO> noConfigUserInfoDOs = punchConfigRangeDao.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
            return noConfigUserInfoDOs.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
        }
        // 查询该打卡规则名称下的所有的用户(根据是否为国家级规则,查询范围不同)
        String ruleNo = ruleConfigSelectDTO.getRuleNo();
        if (StringUtils.isBlank(ruleNo)) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleNo is required");
        }
        // 根据规则编码查询规则配置
        PunchConfigBO punchConfigBO = punchConfigManage.getPunchConfigBO(ruleNo);
        if (punchConfigBO == null) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleNo is invalid");
        }
        List<PunchConfigRangeDO> rangeDOList = punchConfigBO.getRangeDOList();
        return rangeDOList.stream()
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 根据补卡规则配置下拉选择DTO查询用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 用户ID列表
     */
    private List<Long> getReissueCardConfigUsersByRuleSelectDTO(RuleConfigSelectDTO ruleConfigSelectDTO) {
        if (StringUtils.isBlank(ruleConfigSelectDTO.getRuleName())) {
            return Collections.emptyList();
        }
        // 查询所有未配置规则的用户
        if (RuleConfigSelectDTO.isNotConfigRule(ruleConfigSelectDTO.getRuleName())) {
            List<CountryDTO> allCountries = countryService.listAllCountry();
            // 分离普通国家和特殊国家
            List<String> normalCountryList = allCountries.stream()
                    .map(CountryDTO::getCountryShort)
                    .filter(countryShort -> !CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(countryShort))
                    .distinct()
                    .collect(Collectors.toList());

            RuleRangeUserQuery ruleRangeUserQuery = new RuleRangeUserQuery();
            // 设置查询参数
            ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
            ruleRangeUserQuery.setSpecialCountryList(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalCountryList(normalCountryList);

            // 处理用工类型
            ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);

            List<UserInfoDO> noConfigUserInfoDOs = reissueCardConfigRangeDao.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
            return noConfigUserInfoDOs.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
        }
        // 根据规则编码查询规则配置
        ReissueCardConfigBO reissueCardConfigBO = reissueCardConfigManage.getConfigBO(ruleConfigSelectDTO.getRuleNo());
        if (reissueCardConfigBO == null) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleNo is invalid");
        }
        List<ReissueCardConfigRangeDO> rangeDOList = reissueCardConfigBO.getRangeDOList();
        return rangeDOList.stream()
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 根据加班规则配置下拉选择DTO查询用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 用户ID列表
     */
    private List<Long> getOverTimeConfigUsersByRuleSelectDTO(RuleConfigSelectDTO ruleConfigSelectDTO) {
        if (StringUtils.isBlank(ruleConfigSelectDTO.getRuleName())) {
            return Collections.emptyList();
        }
        // 查询所有未配置规则的用户
        if (RuleConfigSelectDTO.isNotConfigRule(ruleConfigSelectDTO.getRuleName())) {
            List<CountryDTO> allCountries = countryService.listAllCountry();
            // 分离普通国家和特殊国家
            List<String> normalCountryList = allCountries.stream()
                    .map(CountryDTO::getCountryShort)
                    .filter(countryShort -> !CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(countryShort))
                    .distinct()
                    .collect(Collectors.toList());

            RuleRangeUserQuery ruleRangeUserQuery = new RuleRangeUserQuery();
            // 设置查询参数
            ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
            ruleRangeUserQuery.setSpecialCountryList(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalCountryList(normalCountryList);

            // 处理用工类型
            ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);

            List<UserInfoDO> noConfigUserInfoDOs = overTimeConfigRangeDao.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
            return noConfigUserInfoDOs.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
        }
        // 根据规则编码查询规则配置
        OverTimeConfigBO overTimeConfigBO = overTimeConfigManage.getConfigBO(ruleConfigSelectDTO.getRuleNo());
        if (overTimeConfigBO == null) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleNo is invalid");
        }
        List<OverTimeConfigRangeDO> rangeDOList = overTimeConfigBO.getRangeDOList();
        return rangeDOList.stream()
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 根据日历规则配置下拉选择DTO查询用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 用户ID列表
     */
    private List<Long> getCalendarConfigUsersByRuleSelectDTO(RuleConfigSelectDTO ruleConfigSelectDTO) {
        if (StringUtils.isBlank(ruleConfigSelectDTO.getRuleName())) {
            return Collections.emptyList();
        }
        if (RuleConfigSelectDTO.isNotConfigRule(ruleConfigSelectDTO.getRuleName())) {
            // 查询在职非司机且未配置日历适用范围的用户列表
            List<UserInfoDO> userInfoDOs = calendarManage.listOnJobNoDriverUsersExcludeRangeConfigured(
                    RuleRangeUserQuery.builder().build());
            return userInfoDOs.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
        }
        // 根据规则编码查询规则配置
        CalendarConfigDO calendarConfigDO = calendarManage.getActiveCalendarConfigById(ruleConfigSelectDTO.getRuleId());
        if (calendarConfigDO == null) {
            throw BusinessLogicException.get(ErrorCodeEnums.PARAM_ERROR.getCode(), "ruleId is invalid");
        }
        List<CalendarConfigRangeDO> calendarConfigRangeDOs = calendarManage
                .selectCalendarConfigRangeByConfigIds(Collections.singletonList(calendarConfigDO.getId()));
        List<Long> userIds = calendarConfigRangeDOs.stream()
                .map(CalendarConfigRangeDO::getBizId)
                .collect(Collectors.toList());
        return userIds;
    }
}
