package com.imile.attendance.permission.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.permission.mapstruct.PermissionMapstruct;
import com.imile.attendance.permission.vo.AttendanceDeptVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 考勤公共权限查询
 *
 * <AUTHOR>
 * @date 2025/4/18
 */
@Service
public class AttendancePermissionServiceImpl implements AttendancePermissionService {
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceDeptService deptService;

    @Override
    public List<String> getUserCountryPermission() {
        // 通过国家+部门权限取并集
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            return Collections.emptyList();
        }
        if (userContext.isSystem()) {
            return countryService.listAllCountry()
                    .stream()
                    .map(item -> item.getCountryName())
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<String> countryCode = Lists.newArrayList();
        // 常驻国权限
        if (CollectionUtils.isNotEmpty(userContext.getCountryList())) {
            countryCode = userContext.getCountryList();
        }
        // 部门权限
        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            List<AttendanceDept> deptList = deptService.listByDeptIds(userContext.getOrganizationIds());
            countryCode.addAll(Optional.ofNullable(deptList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(item -> item.getCountry())
                    .distinct()
                    .collect(Collectors.toList()));
        }
        return countryCode.stream()
                .filter(item -> Objects.nonNull(item))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<AttendanceDeptVO> getUserDeptPermissionByCountry(String country) {
        // 通过国家获取地理国对应的部门与当前拥有的权限部门取交集
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            return Collections.emptyList();
        }
        List<AttendanceDept> attendanceDeptList = deptService.listByCountry(country);
        List<AttendanceDeptVO> attendanceDeptVOList = PermissionMapstruct.INSTANCE.mapToDept(attendanceDeptList);
        // 用户为管理员
        if (userContext.isSystem()) {
            return Optional.ofNullable(attendanceDeptVOList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(attendanceDeptVOList)) {
            return Collections.emptyList();
        }
        // 部门权限
        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            return attendanceDeptVOList.stream()
                    .filter(item -> userContext.getOrganizationIds().contains(item.getId()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
