package com.imile.attendance.infrastructure.repository.form.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.mapper.AttendanceFormMapper;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceFormDaoImpl extends ServiceImpl<AttendanceFormMapper, AttendanceFormDO> implements AttendanceFormDao {

    @Resource
    private AttendanceFormMapper attendanceFormMapper;

    @Override
    public List<AttendanceFormDO> selectForm(ApplicationFormQuery query) {
        LambdaQueryWrapper<AttendanceFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormDO::getIsDelete, BusinessConstant.N);
        if (query.getUserId() != null) {
            queryWrapper.eq(AttendanceFormDO::getUserId, query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getApplicationFormCode())) {
            queryWrapper.eq(AttendanceFormDO::getApplicationCode, query.getApplicationFormCode());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(AttendanceFormDO::getUserId, query.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            queryWrapper.in(AttendanceFormDO::getFormStatus, query.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getFromTypeList())) {
            queryWrapper.in(AttendanceFormDO::getFormType, query.getFromTypeList());
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query) {
        LambdaQueryWrapper<AttendanceFormDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceFormDO::getIsDelete, BusinessConstant.N);
        if (query.getApplyUserId() != null) {
            queryWrapper.eq(AttendanceFormDO::getApplyUserId, query.getApplyUserId());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(AttendanceFormDO::getUserId, query.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getFormStatusList())) {
            queryWrapper.in(AttendanceFormDO::getFormStatus, query.getFormStatusList());
        }
        if (StringUtils.isNotBlank(query.getUserName())) {
            queryWrapper.like(AttendanceFormDO::getUserName, query.getUserName());
        }
        if (StringUtils.isNotBlank(query.getUserCodeOrName())) {
            queryWrapper.and(wrapper -> wrapper.like(AttendanceFormDO::getUserCode, query.getUserCodeOrName())
                    .or()
                    .like(AttendanceFormDO::getUserName, query.getUserCodeOrName()));
        }
        if (query.getDeptId() != null) {
            queryWrapper.eq(AttendanceFormDO::getDeptId, query.getDeptId());
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getDeptIds()), AttendanceFormDO::getDeptId, query.getDeptIds());
        if (query.getPostId() != null) {
            queryWrapper.eq(AttendanceFormDO::getPostId, query.getPostId());
        }
        if (CollectionUtils.isNotEmpty(query.getFormTypeList())) {
            queryWrapper.in(AttendanceFormDO::getFormType, query.getFormTypeList());
        }
        if (query.getStartDate() != null) {
            queryWrapper.ge(AttendanceFormDO::getCreateDate, query.getStartDate());
        }
        if (query.getEndDate() != null) {
            queryWrapper.le(AttendanceFormDO::getCreateDate, query.getEndDate());
        }
        if (StringUtils.isNotBlank(query.getApplicationFormCode())) {
            queryWrapper.like(AttendanceFormDO::getApplicationCode, query.getApplicationFormCode());
        }
        if (StringUtils.isNotBlank(query.getExcludeApplicationDataSource())) {
            //TODO ne有问题，为空无法判断
            queryWrapper.isNull(AttendanceFormDO::getDataSource);
            //queryWrapper.ne(HrmsApplicationFormDO::getDataSource, query.getExcludeApplicationDataSource());
        }
        if (StringUtils.isNotBlank(query.getCountry())) {
            queryWrapper.eq(AttendanceFormDO::getCountry, query.getCountry());
        }
        queryWrapper.orderByDesc(AttendanceFormDO::getLastUpdDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceFormDO> selectAttendanceApprovalInfoCustom(AttendanceApprovalInfoQuery query) {
        return attendanceFormMapper.selectAttendanceApprovalInfo(query);
    }
}

