package com.imile.attendance.infrastructure.repository.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
@Mapper
@Repository
public interface PunchConfigRangeMapper extends BaseMapper<PunchConfigRangeDO> {

    /**
     * 统计国家下在职非司机且未配置规则的用户总数
     *
     * @param country 国家代码
     * @return 用户总数
     */
    Integer countOnJobNoDriverNotConfiguredUsers(@Param("country") String country, @Param("employeeTypes") List<String> employeeTypes);

    /**
     * 统计国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 统计多个国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery);

    /**
     * 根据日期范围查询规则范围
     *
     * @param query
     * @return
     */
    List<PunchConfigRangeDO> selectConfigRangeByDate(PunchConfigRangeByDateQuery query);
}
