package com.imile.attendance.infrastructure.repository.common;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.PermissionTypeEnum;
import com.imile.attendance.enums.SystemRoleEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.permission.dto.DataPermissionDTO;
import com.imile.attendance.permission.dto.PermissionDTO;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionDeptVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 用户权限服务
 */
@Service
public class UserResourceService {

    @Value("${dept.root.id:1032652}")
    private Long rootDeptId;

    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserPermissionService userPermissionService;

    /**
     * 获取用户有权限的业务覆盖国列表
     *
     * @param userId 用户ID
     * @return List<String>
     */
    public List<String> getAuthorizedBizCountryList(Long userId) {
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (Objects.isNull(attendanceUser)) {
            return Collections.emptyList();
        }
        PermissionDTO userPermission = userPermissionService.getUserPermission(attendanceUser.getUserCode());
        // 角色列表
        List<Long> roleList = userPermission.getRoleList();
        // 系统管理员
        if (CollectionUtils.isNotEmpty(roleList) && SystemRoleEnum.isSystem(roleList)) {
            List<AttendanceDept> firstDeptList = deptService.selectFirstDeptByRoot(rootDeptId, Boolean.TRUE);
            List<String> firstDeptBizCountryList = firstDeptList.stream()
                    .map(dept -> Splitter.on(StrUtil.COMMA).splitToList(dept.getBizCountry()))
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
            firstDeptBizCountryList.add(CountryCodeEnum.HQ.getCode());
            return firstDeptBizCountryList;
        }

        // 用户权限
        List<Long> organizationIds = new ArrayList<>();
        List<DataPermissionDTO> dataPermissionDTOList = userPermission.getDataPermissionDTOList();
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            // 数据权限Map
            Map<String, List<String>> permissionMap = dataPermissionDTOList.stream()
                    .collect(Collectors.toMap(DataPermissionDTO::getTypeCode, DataPermissionDTO::getDataCodeList));
            // 获取部门数据权限
            List<String> deptIdList = permissionMap.get(PermissionTypeEnum.DEPT.getTypeCode());
            if (CollectionUtils.isNotEmpty(deptIdList)) {
                organizationIds.addAll(
                        deptIdList.stream()
                                .map(Long::parseLong)
                                .collect(Collectors.toList())
                );
            }
        }

        // 无任何权限
        if (CollectionUtils.isEmpty(organizationIds)) {
            return Collections.emptyList();
        }

        List<AttendanceDept> deptList = deptService.listByDeptIds(organizationIds);
        return deptList.stream()
                .map(dept -> Splitter.on(StrUtil.COMMA).splitToList(dept.getBizCountry()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 获取当前用户有权限的业务覆盖国列表
     *
     * @return List<String>
     */
    public List<String> getAuthorizedBizCountryList() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();

        // 系统管理员
        if (userContext.isSystem()) {
            List<AttendanceDept> firstDeptList = deptService.selectFirstDeptByRoot(rootDeptId, Boolean.TRUE);
            List<String> firstDeptBizCountryList = firstDeptList.stream()
                    .map(dept -> Splitter.on(StrUtil.COMMA).splitToList(dept.getBizCountry()))
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
            firstDeptBizCountryList.add(CountryCodeEnum.HQ.getCode());
            return firstDeptBizCountryList;
        }

        // 获取部门权限
        List<Long> organizationIds = userContext.getOrganizationIds();

        // 无任何权限
        if (CollectionUtils.isEmpty(organizationIds)) {
            return Collections.emptyList();
        }

        List<AttendanceDept> deptList = deptService.listByDeptIds(organizationIds);
        return deptList.stream()
                .map(dept -> Splitter.on(StrUtil.COMMA).splitToList(dept.getBizCountry()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 获取当前用户有权限的业务覆盖国列表
     *
     * @return List<String>
     */
    public List<String> getAuthorizedBizCountryList(List<String> bizCountryList){
        bizCountryList = bizCountryList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> authorizedBizCountryList = this.getAuthorizedBizCountryList();
        if (CollectionUtils.isEmpty(bizCountryList)) {
            return authorizedBizCountryList;
        }
        if (CollectionUtils.isEmpty(authorizedBizCountryList)) {
            return Collections.emptyList();
        }
        authorizedBizCountryList.retainAll(bizCountryList);
        return authorizedBizCountryList;
    }

    /**
     * 根据用户id获取权限
     * @param userId
     * @return
     */
    public PermissionDeptVO getPermissionDept(Long userId) {
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (Objects.nonNull(userInfo)) {
            return getPermissionDept(userInfo.getUserCode());
        } else {
            return new PermissionDeptVO(Boolean.FALSE, Boolean.FALSE, new ArrayList<>());
        }
    }

    /**
     * 根据用户编码获取权限
     * @param userCode
     * @return
     */
    public PermissionDeptVO getPermissionDept(String userCode) {
        PermissionDTO userPermission = userPermissionService.getUserPermission(userCode);
        // 角色列表
        List<Long> roleList = userPermission.getRoleList();
        // 保存角色列表
        if (CollectionUtils.isNotEmpty(roleList)) {
            // 系统管理员角色
            if (SystemRoleEnum.isSystem(roleList)) {
                return new PermissionDeptVO(Boolean.TRUE, Boolean.TRUE, new ArrayList<>());
            }
        }
        List<DataPermissionDTO> dataPermissionDTOList = userPermission.getDataPermissionDTOList();
        if (CollectionUtils.isNotEmpty(dataPermissionDTOList)) {
            // 数据权限Map
            Map<String, List<String>> permissionMap = dataPermissionDTOList.stream().collect(Collectors.toMap(DataPermissionDTO::getTypeCode, DataPermissionDTO::getDataCodeList));
            // 获取部门数据权限
            List<String> deptIdList = permissionMap.get(PermissionTypeEnum.DEPT.getTypeCode());
            if (CollectionUtils.isNotEmpty(deptIdList)) {
                return new PermissionDeptVO(Boolean.FALSE, Boolean.TRUE, deptIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
            }
        }
        return new PermissionDeptVO(Boolean.FALSE, Boolean.FALSE, new ArrayList<>());
    }


    /**
     * 根据当前用户权限部门id列表
     *
     * @return PermissionDeptVO
     */
    public PermissionDeptVO getPermissionDept(){
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        // 系统管理员
        if (userContext.isSystem()) {
            return new PermissionDeptVO(Boolean.TRUE, Boolean.TRUE, new ArrayList<>());
        }
        // 获取部门权限
        List<Long> organizationIds = userContext.getOrganizationIds();

        // 非系统管理员，且部门权限为空，返回空数据权限
        if (CollectionUtils.isEmpty(organizationIds)) {
            return new PermissionDeptVO(Boolean.FALSE, Boolean.FALSE, new ArrayList<>());
        } else {
            return new PermissionDeptVO(Boolean.FALSE, Boolean.TRUE, organizationIds);
        }
    }

    /**
     * 根据当前用户id,和指定部门获取有权限部门id列表
     *
     * @param userDeptIdList 指定部门
     * @return PermissionDeptVO
     */
    public PermissionDeptVO getPermissionDept(List<Long> userDeptIdList){
        PermissionDeptVO permissionDept = this.getPermissionDept();
        if (CollectionUtils.isEmpty(userDeptIdList)) {
            return permissionDept;
        }
        if (permissionDept.getIsSysAdmin()) {
            permissionDept.setHasDeptPermission(Boolean.TRUE);
            permissionDept.setDeptIdList(userDeptIdList);
            return permissionDept;
        }
        List<Long> deptIdList = permissionDept.getDeptIdList();
        userDeptIdList.retainAll(deptIdList);
        permissionDept.setHasDeptPermission(CollectionUtils.isNotEmpty(userDeptIdList));
        permissionDept.setDeptIdList(userDeptIdList);
        return permissionDept;
    }


    public PermissionCountryDeptVO getPermissionCountryDeptVO(List<Long> deptIds, List<String> country){
        PermissionCountryDeptVO permissionDept = this.getPermissionCountryDeptVO();

        // 部门
        if (CollectionUtils.isNotEmpty(deptIds)) {
            if (permissionDept.getIsSysAdmin()) {
                permissionDept.setDeptIdList(deptIds);
            } else {
                List<Long> deptIdList = permissionDept.getDeptIdList();
                Collection<Long> intersectionDept = CollectionUtils.intersection(deptIds, deptIdList);
                if (CollectionUtils.isNotEmpty(intersectionDept)) {
                    permissionDept.setDeptIdList(new ArrayList<>(intersectionDept));
                } else {
                    permissionDept.setHasDeptPermission(Boolean.FALSE);
                    permissionDept.setDeptIdList(new ArrayList<>());
                }
            }
        }

        // 国家
        if (CollectionUtils.isNotEmpty(country)) {
            if (permissionDept.getIsSysAdmin()) {
                permissionDept.setCountryList(country);
            } else {
                List<String> countryList = permissionDept.getCountryList();
                Collection<String> intersectionCountry = CollectionUtils.intersection(country, countryList);
                if (CollectionUtils.isNotEmpty(intersectionCountry)) {
                    permissionDept.setCountryList(new ArrayList<>(intersectionCountry));
                } else {
                    permissionDept.setHasCountryPermission(Boolean.FALSE);
                    permissionDept.setCountryList(new ArrayList<>());
                }
            }
        }

        // 非系统管理员
        if (!permissionDept.getIsSysAdmin()) {
            // 部门国家权限
            if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) && CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
                permissionDept
                        .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) || CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
                    permissionDept
                            .setHasOrDeptAndCountryPermission(Boolean.TRUE);
                } else {
                    permissionDept
                            .setHasOrDeptAndCountryPermission(Boolean.FALSE);
                }
                permissionDept
                        .setHasAndDeptAndCountryPermission(Boolean.FALSE);
            }
        }

        return permissionDept;
    }

    public PermissionCountryDeptVO getPermissionCountryDeptVO() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        PermissionCountryDeptVO permissionCountryDeptVO = new PermissionCountryDeptVO();
        // 系统管理员
        if (userContext.isSystem()) {
            return permissionCountryDeptVO
                    .setIsSysAdmin(Boolean.TRUE)
                    .setHasDeptPermission(Boolean.TRUE)
                    .setHasCountryPermission(Boolean.TRUE)
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE)
                    .setDeptIdList(new ArrayList<>())
                    .setCountryList(new ArrayList<>());
        } else {
            permissionCountryDeptVO
                    .setIsSysAdmin(Boolean.FALSE);
        }

        // 部门
        List<Long> organizationIds = userContext.getOrganizationIds();
        if (CollectionUtils.isNotEmpty(organizationIds)) {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.TRUE)
                    .setDeptIdList(organizationIds);
        } else {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.FALSE)
                    .setDeptIdList(new ArrayList<>());
        }

        // 国家
        List<String> countryList = userContext.getCountryList();
        if (CollectionUtils.isNotEmpty(countryList)) {
            permissionCountryDeptVO
                    .setHasCountryPermission(Boolean.TRUE)
                    .setCountryList(countryList);
        } else {
            permissionCountryDeptVO
                    .setHasCountryPermission(Boolean.FALSE)
                    .setCountryList(new ArrayList<>());
        }

        // 部门国家权限
        if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) && CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE);
        } else {
            if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) || CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.FALSE);
            }
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.FALSE);
        }

        return permissionCountryDeptVO;
    }

}
