package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;


import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceUserCardConfigDao extends IService<UserCycleReissueCardCountDO> {

    /**
     * 查询用户的所有考勤周期的打卡配置
     */
    List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList);

}

