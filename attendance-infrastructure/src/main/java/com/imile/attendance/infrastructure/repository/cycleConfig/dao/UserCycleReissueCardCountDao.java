package com.imile.attendance.infrastructure.repository.cycleConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.UserCycleReissueCardCountDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
public interface UserCycleReissueCardCountDao extends IService<UserCycleReissueCardCountDO> {

    /**
     * 查询用户的所有考勤周期的补卡次数配置
     */
    List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList);
}
