package com.imile.attendance.infrastructure.repository.rule.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchRangeConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigRangeByDateQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class PunchConfigRangeDaoImpl extends ServiceImpl<PunchConfigRangeMapper, PunchConfigRangeDO>
        implements PunchConfigRangeDao {

    @Override
    public List<PunchConfigRangeDO> listConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getBizId, userIds)
                .eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listActivedConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getBizId, userIds)
                .eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listNotDeletedConfigRanges(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PunchConfigRangeDO::getBizId, userIds)
                .eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listAllConfigRanges(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getBizId, userId)
                .eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .orderByDesc(PunchConfigRangeDO::getCreateDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(PunchConfigRangeDO.class);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode())
                .eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .in(PunchConfigRangeDO::getRuleConfigId, configIdList);
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listNotDeletedByConfigIds(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(PunchConfigRangeDO.class);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y)
                .in(PunchConfigRangeDO::getRuleConfigId, configIdList);
        return list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listActivedConfigByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listDisabledByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(PunchConfigRangeDO::getStatus, StatusEnum.DISABLED.getCode());
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listNotDeletedByConfigId(Long configId) {
        if (configId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PunchConfigRangeDO::getRuleConfigId, configId);
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listPunchConfigRangeByQuery(PunchRangeConfigQuery userQuery) {
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
        if (CollectionUtils.isNotEmpty(userQuery.getBizIds())) {
            queryWrapper.in(PunchConfigRangeDO::getBizId, userQuery.getBizIds());
        }
        if (userQuery.getRangeType() != null) {
            queryWrapper.eq(PunchConfigRangeDO::getRangeType, userQuery.getRangeType());
        }
        if (userQuery.getStartTime() != null && userQuery.getEndTime() != null) {
            queryWrapper.and(param ->
                    param.ge(PunchConfigRangeDO::getExpireTime, userQuery.getStartTime())
                            .and(i -> i.lt(PunchConfigRangeDO::getEffectTime, userQuery.getEndTime())));
        }
        queryWrapper.eq(PunchConfigRangeDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.eq(PunchConfigRangeDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<PunchConfigRangeDO> listAllRangeByUserIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery(PunchConfigRangeDO.class);
        queryWrapper.eq(PunchConfigRangeDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .in(PunchConfigRangeDO::getBizId, userIdList)
                .orderByDesc(PunchConfigRangeDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public Integer countOnJobNoDriverNotConfiguredUsers(String country) {
        if (StringUtils.isEmpty(country)) {
            return 0;
        }
        List<String> employeeTypes = CommonUserService.getCountryEmployeeTypes(country);
        return this.baseMapper.countOnJobNoDriverNotConfiguredUsers(country, employeeTypes);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        //多个国家单独处理
        if (CollectionUtils.isNotEmpty(ruleRangeUserQuery.getCountries())) {
            Map<Boolean, List<String>> countryMap = ruleRangeUserQuery.getCountries().stream()
                    .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));
            List<String> normalCountryList = countryMap.getOrDefault(false, Collections.emptyList());
            List<String> specialCountryList = countryMap.getOrDefault(true, Collections.emptyList());
            if (CollectionUtils.isNotEmpty(normalCountryList)) {
                ruleRangeUserQuery.setNormalCountryList(normalCountryList);
                ruleRangeUserQuery.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
            if (CollectionUtils.isNotEmpty(specialCountryList)) {
                ruleRangeUserQuery.setIsNeedQuerySpecialCountry(true);
                ruleRangeUserQuery.setSpecialCountryList(specialCountryList);
                ruleRangeUserQuery.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
            return this.baseMapper.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
        }
        if (StringUtils.isNotBlank(ruleRangeUserQuery.getCountry())) {
            List<String> employeeTypes = CommonUserService.getCountryEmployeeTypes(ruleRangeUserQuery.getCountry());
            ruleRangeUserQuery.setEmployeeTypeList(employeeTypes);
        }
        return this.baseMapper.listOnJobNoDriverUsersExcludeConfigured(ruleRangeUserQuery);
    }

    @Override
    public List<UserInfoDO> listOnJobNoDriverMultiCountryUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery) {
        return this.baseMapper.listOnJobNoDriverMultiCountryUsersExcludeConfigured(ruleRangeUserQuery);
    }

    @Override
    public List<PunchConfigRangeDO> selectConfigRangeByDate(PunchConfigRangeByDateQuery query) {
        return this.baseMapper.selectConfigRangeByDate(query);
    }
}
