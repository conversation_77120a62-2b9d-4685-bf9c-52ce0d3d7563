package com.imile.attendance.infrastructure.repository.cycleConfig.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.mapper.UserCycleReissueCardCountMapper;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.UserCycleReissueCardCountDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserCycleReissueCardCountDaoImpl extends ServiceImpl<UserCycleReissueCardCountMapper, UserCycleReissueCardCountDO> implements UserCycleReissueCardCountDao {


    @Override
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserCycleReissueCardCountDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserCycleReissueCardCountDO::getUserId, userIdList);
        wrapper.eq(UserCycleReissueCardCountDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(wrapper);
    }
}
