package com.imile.attendance.infrastructure.repository.form.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
@ApiModel(description = "用户周期补卡次数表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_user_card_config")
public class AttendanceUserCardConfigDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 当前周期已经使用的补卡次数
     */
    @ApiModelProperty(value = "当前周期已经使用的补卡次数")
    private Integer usedCardCount;

    /**
     * 本次周期开始时间
     */
    @ApiModelProperty(value = "本次周期开始时间")
    private Date cycleStartDate;

    /**
     * 本次周期结束时间
     */
    @ApiModelProperty(value = "本次周期结束时间")
    private Date cycleEndDate;
}

