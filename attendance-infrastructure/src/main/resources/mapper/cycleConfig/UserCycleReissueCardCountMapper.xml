<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.cycleConfig.mapper.UserCycleReissueCardCountMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.cycleConfig.model.UserCycleReissueCardCountDO">
        <!--@mbg.generated-->
        <!--@Table user_cycle_reissue_card_count-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="used_reissue_card_count" jdbcType="TINYINT" property="usedReissueCardCount" />
        <result column="cycle_start_date" jdbcType="TIMESTAMP" property="cycleStartDate" />
        <result column="cycle_end_date" jdbcType="TIMESTAMP" property="cycleEndDate" />
        <!-- 以下为继承自BaseDO的通用字段，即使DO类中不显式定义，也需在此处映射 -->
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, used_reissue_card_count, cycle_start_date, cycle_end_date,
        is_delete, record_version, create_date, create_user_code, create_user_name,
        last_upd_date, last_upd_user_code, last_upd_user_name
    </sql>
</mapper>
