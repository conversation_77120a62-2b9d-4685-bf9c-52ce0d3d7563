package com.imile.attendance.constants;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.util.DateConvertUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
public class BusinessConstant {

    public static final String SYSTEM_CODE = "Attendance";

    public static final Integer Y = 1;
    public static final Integer N = 0;


    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer TWO = 2;

    public static final Long DEFAULT_ORG_ID = 10L;

    public static final String DEFAULT_DELIMITER = ",";

    public static final String DEFAULT_HYPHEN = "-";

    public static final String CHINESE_PATTERN = "[\u4e00-\u9fa5]";

    public static final Integer MONTH_YEAR = 12;
    public static final Integer MONTH_DAY = 31;
    public static final Integer HOUR_DAY_LAST = 23;

    /**
     * 一百
     */
    public static final BigDecimal HUNDRED = new BigDecimal(100);
    public static final String ONE_HUNDRED = "100";
    public static final Integer ONE_HUNDRED_NUM = 100;

    /**
     * 一年
     */
    public static final BigDecimal ONE_YEAR = new BigDecimal(365);
    /**
     * 一月
     */
    public static final BigDecimal ONE_MONTH = new BigDecimal(30);
    /**
     * 一周
     */
    public static final BigDecimal ONE_WEEK = new BigDecimal(7);
    /**
     * 天偏移量
     */
    public static final Integer DEFAULT_OFFSET = -1;
    /**
     * 周期偏移量
     */
    public static final Integer DEFAULT_CYCLE_OFFSET = 0;

    /**
     * 默认时间
     * 1970-01-01
     */
    public static final String DEFAULT_TIME = "1970-01-01 ";

    public static final Integer DEFAULT_TIMEZONE = 8;

    /**
     * 一天的小时数
     */
    public static final BigDecimal HOURS = new BigDecimal("24");

    /**
     * 一小时的分钟数
     */
    public static final BigDecimal MINUTES = new BigDecimal("60");

    /**
     * 一天的分钟数
     */
    public static final BigDecimal DAYS_MINUTES = new BigDecimal("1440");

    /**
     * 默认的endTimeStamp 4102329599000L
     */
    public static final Long DEFAULT_END_TIMESTAMP = 4102329599000L;

    /**
     * 默认的endTime 2099-12-30 23:59:59
     */
    public static final Date DEFAULT_END_TIME = new Date(DEFAULT_END_TIMESTAMP);

    /**
     * 默认的endTimeStr 2099-12-30 23:59:59
     */
    public static final String DEFAULT_END_TIME_STRING = "2099-12-30 23:59:59";

    public static final Date DEFAULT_END_TIME_DATE = DateUtil.parse(DEFAULT_END_TIME_STRING, DateConvertUtils.FORMAT_DATE_TIME);

    /**
     * 默认每天的法定工作时长
     */
    public static final BigDecimal DEFAULT_LEGAL_WORKING_HOURS = new BigDecimal("8");
    /**
     * 全球标识
     */
    public static final String GLOBAL_FLAG = "Global";

    public static final String PERCENT = "%";

    /**
     * 自动排班最大天数 180天
     */
    public final static Integer MAX_AUTO_SHIFT_DAYS = 180;

    /**
     * 批量处理的最大数量限制
     */
    public static final Integer MAX_BATCH_SIZE = 500;


    /**
     * 考勤日
     */
    public static final String ATTENDANCE_DAY_ID = "attendanceDayId";

    /**
     * 班次主键
     */
    public static final String PUNCH_CLASS_ID = "punchClassId";

    /**
     * 班次详情主键
     */
    public static final String PUNCH_CLASS_ITEM_ID = "punchClassItemId";

    /**
     * 移动打卡请求有效时间,5分钟
     */
    public static final long EXPIRE_TIME_INTERNAL_MILLISECONDS = 5 * 60 * 1000L;

    /**
     * 仓内过滤国家
     */
    public static final List<String> WAREHOUSE_COUNTRY_FILTER = Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode());

    public static class SysDictDataTypeConstant {
        private SysDictDataTypeConstant() {
        }

        public static final String SYS_BOOLEAN = "SysBoolean";
        public static final String SEX = "SEX";
        public static final String MARITAL_STATUS = "maritalStatus";
        public static final String EDUCATION = "education";
        public static final String EMPLOYEE_TYPE = "employeeType";
        public static final String EMPLOYMENT_TYPE = "EmploymentType";
        public static final String CLASS_NATURE = "ClassNature";
        public static final String SALARY_SETTLEMENT_FORM_STATUS_ENUM = "SalarySettlementFormStatusEnum";
        public static final String SALARY_SETTLEMENT_FORM_SUBMIT_TYPE_ENUM = "SalarySettlementFormSubmitTypeEnum";
        public static final String SALARY_SUBMIT_TEMPLATE_COLLECTION_TYPE_ENUM = "SalarySubmitTemplateCollectionTypeEnum";
        public static final String SALARY_SUBMIT_TEMPLATE_APPROVAL_METHOD_ENUM = "SalarySubmitTemplateApprovalMethodEnum";
        public static final String SALARY_SETTLEMENT_USER_STATUS_ENUM = "SalarySettlementUserStatusEnum";
        public static final String SALARY_SETTLEMENT_USER_ITEM_STATUS_ENUM = "SalarySettlementUserItemStatusEnum";
        public static final String ENTRY_STATUS = "entryStatus";
        public static final String VISA_TYPE_OLD = "visaType";
        public static final String WORK_STATUS = "workStatus";
        public static final String TRANSFER_STATUS = "transferStatus";
        public static final String CERTIFICATE_TYPE_CODE = "certificateTypeCode";

        public static final String ACCOUNT_STATUS = "accountStatus";

        public static final String FMS_ACCOUNT_STATUS = "fmsAccountStatus";

        public static final String EMERGENCY_RELATION = "EmergencyRelation";

        public static final String DRIVER_TYPE = "driverType";

        public static final String HRMS_ABNORMAL_TYPE = "HrmsAbnormalType";

        public static final String WAREHOUSE_ABNORMAL_TYPE = "WarehouseAbnormalType";

        public static final String HRMS_ATTENDANCE_DAY_TYPE = "HrmsAttendanceDayType";

        public static final String HRMS_ATTENDANCE_LEAVE_TYPE = "LeaveTypeConfig";

        public static final String ATTENDANCE_ABNORMAL_OPERATION_TYPE = "AttendanceAbnormalOperationTypeEnum";
        public static final String HR_ATTENDANCE_APPLICATION_FORM_STATUS = "HrAttendanceApplicationFormStatusEnum";


        /**
         * 离职状态
         */
        public static final String DIMISSION_STATUS = "dimissionStatus";
        public static final String ADMINISTRATOR_ATTRIBUTES = "administratorAttribute";

        /**
         * Hrms账号状态
         */
        public static final String HRMS_ACCOUNT_STATUS = "HrmsAccountStatus";

        /**
         * 入职渠道
         */
        public static final String HRMS_ONBOARDING_CHANNEL = "onboardingChannel";

        /**
         * 奖惩类型
         */
        public static final String HRMS_OWN_INCENTIVE_TYPE = "ownIncentiveType";

        /**
         * 计薪方式
         */
        public static final String HRMS_SALARY_METHOD = "HrmsSalaryMethod";
        /**
         * 计薪周期类型
         */
        public static final String HRMS_CYCLE_TYPE = "HrmsCycleType";
        /**
         * 缴纳基本类型
         */
        public static final String BASE_PAYMENT_TYPE = "basePaymentType";
        /**
         * 输入类型
         */
        public static final String INPUT_TYPE = "InputType";
        /**
         * 上班规则配置
         */
        public static final String PUNCH_TYPE = "HrmsPunchConfigType";
        /**
         * 加班补贴方式
         */
        public static final String SUBSIDY_TYPE = "HrmsAllowanceType";
        /**
         * 日期类型
         */
        public static final String DATE_TYPE = "hrmsDateType";

        /**
         * 打卡方式
         */
        public static final String PUNCH_CARD_TYPE = "PunchCardTypeSearch";
        /**
         * 打卡数据来源
         */
        public static final String PUNCH_SOURCE_TYPE = "HrmsPunchCardType";

        /**
         * 培训类型
         */
        public static final String TRAINING_TYPE = "TrainingType";

        /**
         * 培训状态
         */
        public static final String TRAINING_STATUS = "TrainingStatusEnum";

        /**
         * 培训状态
         */
        public static final String TRAINING_USER_STATUS = "TrainingUserStatusEnum";

        /**
         * 众包司机审核类型
         */
        public static final String REVIEW_TYPE = "reviewType";
        public static final String FREELANCER_DRIVER_AUDIT_TYPE = "FreelancerDriverAuditType";
        /**
         * 黑名单封禁状态
         */
        public static final String BAN_STATUS = "banStatus";

        /**
         * 调动类型
         */
        public static final String TRANSFER_TYPE = "hrmsTransferType";

        /**
         * 业务领域
         */
        public static final String BIZ_AREA = "bizArea";

        /**
         * 组织类型
         */
        public static final String DEPT_TYPE = "deptType";

        /**
         * 网点类型
         */
        public static final String OC_TYPE = "OcType";

        /**
         * 业务负责人属性
         */
        public static final String LEADER_PROPERTY = "leaderProperty";

        /**
         * 支付方式
         */
        public static final String ERS_PAYMENT_METHOD = "ErsPaymentMethod";

        /**
         * 是否
         */
        public static final String WHETHER = "YesOrNo";

        /**
         * 驾驶证等级
         */
        public static final String DRIVER_LICENSE_LEVEL = "DriverLicenseLevel";

        /**
         * 开户银行
         */
        public static final String ACCOUNT_BANK_NAME = "AccountBankName";

        /**
         * 工作经验
         */
        public static final String WORK_EXPERIENCE = "WorkExperience";

        public static final String OFFER_STATUS_ENUM = "OfferStatusEnum";

        public static final String HC_STATUS_ENUM = "HcStatusEnum";

        public static final String HC_REQUEST_TYPE_ENUM = "HcRequestTypeEnum";

        public static final String SALARY_BUDGET_TYPE_ENUM = "SalaryBudgetTypeEnum";

        public static final String BUDGET_ALLOCATION_TYPE_ENUM = "BudgetAllocationTypeEnum";

        public static final String IT_ASSETS_REQUIRED_TYPE_ENUM = "ITAssetsRequiredTypeEnum";

        public static final String HC_APPROVAL_INFO_STATUS_ENUM = "HcApprovalInfoStatusEnum";

        public static final String RECRUITMENT_TYPE_ENUM = "RecruitmentTypeEnum";

        public static final String RECRUITMENT_EMAIL_SEND_STATUS_ENUM = "RecruitmentEmailSendStatusEnum";
        public static final String FUNCTION_TYPE_ENUM = "FunctionTypeEnum";

        /**
         * 薪资项属性
         */
        public static final String SALARY_ITEM_ATTRIBUTE = "SalaryItemAttributeEnum";

        /**
         * OA费用项
         */
        public static final String OA_COST_TYPE = "OACostType";

        /**
         * 薪资项格式
         */
        public static final String SALARY_ITEM_FORMAT = "SalaryItemFormatEnum";

        /**
         * 薪资项取值方式
         */
        public static final String SALARY_ITEM_VALUE_TYPE = "SalaryItemValueTypeEnum";

        /**
         * 薪资项取值-报表展示
         */
        public static final String SALARY_ITEM_REPORT_VALUE = "SalaryItemReportValueEnum";

        /**
         * 薪资项类型
         */
        public static final String SALARY_ITEM_TYPE = "SalaryItemTypeEnum";

        /**
         * 计薪方案周期类型
         */
        public static final String SALARY_SCHEME_CYCLE_TYPE = "SalarySchemeCycleTypeEnum";


        /**
         * 发薪日期(周)
         */
        public static final String SALARY_PAY_DATA_WEEK = "SalaryPayDataWeekEnum";


        /**
         * 发薪日期(双周)
         */
        public static final String SALARY_PAY_DATA_DOUBLE_WEEK = "SalaryPayDataDoubleWeekEnum";

        /**
         * 薪资操作类型
         */
        public static final String SALARY_OPERATION_TYPE = "SalaryOperationTypeEnum";

        /**
         * 考勤报表配置类型
         */
        public static final String SALARY_REPORT_TYPE = "SalaryReportTypeEnum";

        /**
         * 薪资计算任务状态
         */
        public static final String SALARY_CALCULATE_TASK_STATUS = "SalaryCalculateTaskStatus";

        /**
         * 招聘操作日志行为类型
         */
        public static final String RECRUITMENT_OPERATION_ACTION_TYPE = "RecruitmentOperationActionType";

        public static final String RECRUITMENT_OPERATION_OFFER_ACTION_TYPE = "RecruitmentOperationOfferActionType";

        /**
         * 数据审批方式（提报后需审批MUST_APPROVAL / 提报后无需审批NONE_APPROVAL）
         */
        public static final String SALARY_APPROVAL_METHOD = "SalaryApprovalMethod";

        /**
         * 薪资计算任务计算人员类型（入职、离职、本月新增）
         */
        public static final String SALARY_CALCULATE_USER_STATUS_TYPE = "SalaryCalculateUserStatusType";

        /**
         * 薪资提报记录数据状态
         */
        public static final String SALARY_SUBMIT_RECORD_DATA_STATUS = "SalarySubmitRecordDataStatus";

        /**
         * 薪资提报记录操作类型
         */
        public static final String SALARY_SUBMIT_RECORD_OPERATE_TYPE = "SalarySubmitRecordOperateType";

        /**
         * 薪资提报记录数据更新原因
         */
        public static final String SALARY_SUBMIT_RECORD_STATUS_UPDATE_REASON = "SalarySubmitRecordStatusUpdateReason";

        /**
         * 薪资报表配置类型
         */
        public static final String SALARY_REPORT_CONFIG_TYPE = "SalaryReportConfigType";

        /**
         * 签证类型
         */
        public static final String VISA_TYPE = "GlobalVisaType";

        /**
         * 依赖人类型
         */
        public static final String DEPENDENT_TYPE = "DependentType";

        /**
         * 语言类型
         */
        public static final String LANGUAGE_TYPE = "GlobalLanguageType";

        /**
         * 证件字段
         */
        public static final String CERTIFICATE_FIELD = "CertificateField";

        /**
         * 民族
         */
        public static final String NATION = "Nation";

        /**
         * 户口类型
         */
        public static final String RESIDENCE_TYPE = "RegisteredResidenceType";

        /**
         * 政治面貌
         */
        public static final String POLITICAL_AFFILIATION = "PoliticalAffiliation";

        /**
         * 心理测试结果
         */
        public static final String MENTAL_ASSESSMENT_RESULT = "MentalAssessmentResult";

        /**
         * 税收类型
         */
        public static final String TAX_TYPE = "FreelancerDriverTaxType";

        /**
         * 计税方式
         */
        public static final String SALARY_TAXATION_METHOD = "SalaryTaxationMethod";

        /**
         * 生效类型
         */
        public static final String ACTIVE_TYPE = "activeType";

        /**
         * 生效状态
         */
        public static final String ACTIVE_STATUS = "activeTaskStatus";

        /**
         * 试用期转正结果
         */
        public static final String PROBATION_CONFIRMATION_RESULT = "ProbationConfirmationResult";

        /**
         * 试用期操作记录编码
         */
        public static final String PROBATION_LOG_OPERATION_TYPE = "ProbationOperationCode";

        /**
         * 调动状态(new)
         */
        public static final String USER_TRANSFORM_STATUS = "userTransformStatus";

        /**
         * 调动类型(new)
         */
        public static final String USER_TRANSFORM_TYPE = "userTransformType";

        /**
         * 人员合同类型
         */
        public static final String USER_CONTRACT_TYPE = "UserContractType";

        /**
         * 操作场景
         */
        public static final String OPERATION_SCENE = "HrmsOperationScene";
        /**
         * 班次结果确认状态
         */
        public static final String VENDOR_CLASSES_CONFIRM_STATUS = "VendorClassesConfirmStatus";

        /**
         * 仓内黑名单类型
         */
        public static final String WAREHOUSE_BLACK_TYPE = "WarehouseBlackTypeEnum";
        /**
         * 晋升类型
         */
        public static final String PROMOTION_TYPE = "PromotionType";
        /**
         * 晋升状态
         */
        public static final String PROMOTION_STATUS = "PromotionStatus";
    }

    public static class JobHandler {
        private JobHandler() {
        }

        /**
         * 每日 同步 结算主体。
         */
        public static final String RESPONSIBLE_SETTLEMENT_SUBJRCT_SYNC = "responsibleSettlementSubjectSync";

        public static final String TX_CHECK_HANDLER = "txCheckHandler";
        /**
         * PRISM OFD 司机异常，定时巡检解决
         */
        public static final String PRISM_DRIVER_CHECK_HANDLER = "prismDriverCheckHandler";

        /**
         * TMS用户信息同步
         */
        public static final String TMS_USER_INFO_SYNC_HANDLER = "tmsUserInfoSyncHandler";
        /**
         * 未同步到TMS用户检查handler
         */
        public static final String UN_SYNC_USER_INFO_SYNC_HANDLER = "unSyncUserInfoSyncHandler";
        /**
         * 用户角色未同步到TMS检查handler
         */
        public static final String UN_SYNC_USER_ROLE_HANDLER = "unSyncUserRoleHandler";
        /**
         * 日期相关数据初始化
         */
        public static final String DATE_INIT_HANDLER = "dateInitHandler";
        /**
         * 司机每日派件数数据同步
         */
        public static final String DLD_CNT_SYNC_HANDLER = "dldCntSyncHandler";
        /**
         * 薪资明细
         */
        public static final String SALARY_EMPLOYEE_DETAIL_HANDLER = "salaryEmployeeDetailHandler";
        /**
         * 薪资明细
         */
        public static final String SALARY_EMPLOYEE_CONFIG_HANDLER = "salaryEmployeeConfigHandler";
        /**
         * 缺勤数据同步
         */
        public static final String ABSENT_DATA_SYNC_HANDLER = "absentDataSyncHandler";
        /**
         * 用户数据同步
         */
        public static final String USER_PLATFORM_RELATION_SYNC_HANDLER = "userPlatformRelationSyncHandler";
        /**
         * 钉钉考勤数据同步
         */
        public static final String DING_TALK_PUNCH_DATA_SYNC_HANDLER = "dingTalkPunchDataSyncHandler";
        /**
         * 人事调动
         */
        public static final String USER_TRANSFER_HANDLER = "userTransferHandler";
        /**
         * 人事调动
         */
        public static final String PUNCH_STATISTICAL_DATA_PUSH_HANDLER = "punchStatisticalDataPushHandler";
        /**
         * 企业微信员工数据同步
         */
        public static final String WECHAT_USER_INFO_SYNC_HANDLER = "wechatUserInfoSyncHandler";
        /**
         * 校验账号
         */
        public static final String CHECK_USER_ACCOUNT = "checkUserAccount";
        /**
         * 迁移用户证件信息
         */
        public static final String MIGRATION_CERTIFICATE_DATA = "migrationCertificateData";
        /**
         * 外包司机账号状态巡检处理
         */
        public static final String EXTERNAL_DRIVER_ACCOUNT_STATUS_HANDLER = "externalDriverAccountStatusHandler";
        /**
         * 外包司机账号状态巡检处理
         */
        public static final String INTERNAL_DRIVER_ACCOUNT_STATUS_HANDLER = "internalDriverAccountStatusHandler";
        /**
         * hrms员工数据同步到hermes中
         */
        public static final String HRMS_USER_DATA_SYNC_HANDLER = "hrmsUserDataSyncHandler";

        public static final String HRMS_SYNC_INCORRECT_HANDLER = "hrmsSyncIncorrectHandler";
        /**
         * 非管理员角色处理
         */
        public static final String HRMS_USER_ROLE_HANDLER = "hrmsUserRoleHandler";

        /**
         * 同步Hermes中司机的上级姓名/编码到hr系统中
         */
        public static final String LEADER_INFO_SYNC_HANDLER = "leaderInfoSyncHandler";


        /**
         * 同步tms中用户名到hrms到系统账号名称
         */
        public static final String SYNC_USER_NAME_HANDLER = "syncUserNameHandler";
        /**
         * 网点默认权限数据同步
         */
        public static final String NO_PERM_USER_HANDLER = "noPermUserHandler";
        /**
         * hr用户账号和tms侧绑定
         */
        public static final String USER_INFO_BIND_HANDLER = "userInfoBindHandler";
        /**
         * 同步tms侧员工/司机类型到hr系统
         */
        public static final String USER_INFO_TYPE_SYNC_HANDLER = "userInfoTypeSyncHandler";
        /**
         * 补全hrms侧用户信息
         */
        public static final String HRMS_USER_INFO_COMPLETION_HANDLER = "hrmsUserInfoCompletionHandler";

        /**
         * hr侧员工数据校验
         */
        public static final String HRMS_USER_INFO_CHECK_HANDLER = "hrmsUserInfoCheckHandler";
        /**
         * hr侧司机数据校验
         */
        public static final String HRMS_DRIVER_CHECK_HANDLER = "hrmsDriverCheckHandler";

        /**
         * 员工、司机类型修正
         */
        public static final String HRMS_USER_INFO_CORRECTION_HANDLER = "userInfoTypeCorrectionHandler";
        /**
         * 批量处理改变员工账号状态
         */
        public static final String HRMS_USER_ACCOUNT_STATUS_HANDLER = "accountStatusBatchHandler";

        /**
         * 同步tms侧司机职能信息到hr系统
         */
        public static final String SYNC_DRIVER_FUNCTIONAL_HANDLER = "syncDriverFunctionalHandler";

        /**
         * 发送邮件给没有导入员工考勤的负责人/HR
         */
        public static final String ATTENDANCE_EMAIL_HANDLER = "attendanceEmailHandler";

        /**
         * 司机出勤，定时任务
         */
        public static final String DRIVER_ATTENDANCE_HANDLER = "driverAttendanceHandler";

        /**
         * 仓内员工出勤，定时任务
         */
        public static final String WARE_HOUSE_ATTENDANCE_HANDLER = "wareHouseAttendanceHandler";

        /**
         * 同步考勤机关联关系到HR
         */
        public static final String ZKTECO_AREA_RELATION_HANDLER = "zktecoAreaRelationHandler";

        /**
         * 员工岗位订正
         */
        public static final String USER_POST_HANDLER = "userPostHandler";

        /**
         *
         */
        public static final String NEW_USER_IMPORT_HANDLER = "newUserImportHandler";
        /**
         * 员工薪资计算
         */
        public static final String USER_DAILY_SALARY_DETAIL_HANDLER = "userDailySalaryDetailHandler";
        /**
         * 员工假期信息初始化
         */
        public static final String USER_LEAVE_DETAIL_INIT_HANDLER = "userLeaveDetailInitHandler";
        /**
         * 员工假期巡检（如果员工不存在某一种假期，可以使用这个定时任务新增，保证可以获得所属国家）
         */
        public static final String USER_LEAVE_INSPECTION_HANDLER = "userLeaveInspectionHandler";
        /**
         * 员工假期巡检-new（如果员工不存在某一种假期，可以使用这个定时任务新增，保证可以获得所属国家）
         */
        public static final String USER_LEAVE_INSPECTION_NEW_HANDLER = "userLeaveInspectionNewHandler";
        /**
         * 用户假期结转处理
         */
        public static final String USER_LEAVE_CARRY_OVER_HANDLER = "userLeaveCarryOverHandler";
        /**
         * 用户假期失效处理
         */
        public static final String USER_LEAVE_INVALID_HANDLER = "userLeaveInvalidHandler";
        /**
         * 用户假期配置按年龄/工龄发放并符合跨层级新规则时重新计算发放假期
         */
        public static final String USER_LEAVE_REISSUE_HANDLER = "userLeaveReissueHandler";

        /**
         * 员工打卡记录巡检处理
         */
        public static final String EMPLOYEE_PUNCH_RECORD_INSPECTION_HANDLER = "employeePunchRecordInspectionHandler";

        /**
         * 用户假期失效处理
         */
        public static final String USER_LEAVE_DETAIL_CLEAR_HANDLER = "userLeaveDetailClearHandler";
        /**
         * 给用户绑定假期范围
         */
        public static final String USER_LEAVE_BIND_HANDLER = "userLeaveBindHandler";
        /**
         * 员工年假处理类
         */
        public static final String USER_ANNUAL_LEAVE_HANDLER = "userAnnualLeaveHandler";
        /**
         *
         */
        public static final String DRIVER_ATTENDANCE_DETAIL_HANDLER = "driverAttendanceDetailHandler";

        /**
         * 申请单数据刷新
         */
        public static final String BRUSH_DATA_APPLICATION_FORM_HANDLER = "brushDataApplicationFormHandler";

        /**
         * 刷新申请单据的结算国
         */
        public static final String BRUSH_DATA_APPLICATION_FORM_ORIGIN_COUNTRY_HANDLER = "brushDataApplicationFormOriginCountryHandler";

        /**
         * 设置法定假期表中的日历id
         */
        public static final String LEGAL_LEAVE_SET_ATTENDANCE_CONFIG_ID_HANDLER = "legalLeaveSetAttendanceConfigIdHandler";
        /**
         * 查询日历详情表数据中法定假期信息不存在法定假期表中的数据
         */
        public static final String QUERY_ATTENDANCE_CONFIG_NOT_EXIST_LEGAL_LEAVE_DATA_HANDLER = "queryAttendanceConfigNotExistLegalLeaveDataHandler";

        /**
         * 申请单数据刷新
         */
        public static final String BRUSH_DATA_EMPLOYEE_PUNCH_RECORD_HANDLER = "brushDataEmployeePunchRecordHandler";

        /**
         * 外包司机历史数据审核记录初始化
         */
        public static final String HISTORY_USER_AUDIT_RECORD_INIT_HANDLER = "historyUserAuditRecordInitHandler";

        /**
         * 非司机外所有员工历史数据审核记录初始化
         */
        public static final String HISTORY_WAREHOUSE_AUDIT_RECORD_INIT_HANDLER = "historyWarehouseAuditRecordInitHandler";


        /**
         * 同步tms证件信息到hrms系统
         */
        public static final String SYNC_TMS_DRIVER_CERTIFICATE_HANDLER = "syncTmsDriverCertificateHandler";

        /**
         * 异常考勤定时任务
         */
        public static final String ABNORMAL_ATTENDANCE_HANDLER = "abnormalAttendanceHandler";

        /**
         * 刷新用户假期历史数据
         */
        public static final String EMPLOYEE_USER_LEAVE_HISTORY_HANDLER = "employeeUserLeaveHistoryHandler";

        /**
         * 刷新请假历史单据数据
         */
        public static final String EMPLOYEE_USER_LEAVE_FORM_HISTORY_HANDLER = "employeeUserLeaveFormHistoryHandler";

        /**
         * 考勤每日结算
         */
        public static final String ATTENDANCE_GENERATE_HANDLER = "attendanceGenerateHandler";

        /**
         * 考勤每日结算(暂定未打卡人员)
         */
        public static final String ATTENDANCE_DAY_GENERATE_HANDLER = "attendanceDayGenerateHandler";

        /**
         * 考勤每日生成考勤快照(所有人员)
         */
        public static final String ATTENDANCE_DAY_GENERATE_SNAPSHOT_HANDLER = "attendanceDayGenerateSnapshotHandler";

        /**
         * 仓内考勤未出仓打卡结算
         */
        public static final String ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER = "attendanceOutWarehouseMissPunchHandler";

        /**
         * 排班非活跃数据清理
         */
        public static final String SCHEDULING_INACTIVE_DATA_CLEAN_HANDLER = "schedulingInactiveDataCleanHandler";

        /**
         * 离职员工人脸特征向量清理
         */
        public static final String FACE_FEATURE_CLEAN_HANDLER = "faceFeatureCleanHandler";

        /**
         * 考勤每日结算(测试)
         */
        public static final String ATTENDANCE_GENERATE_TEST_HANDLER = "attendanceGenerateTestHandler";

        /**
         * 异常考勤-企业微信提醒
         */
        public static final String ABNORMAL_ATTENDANCE_REMINDER_HANDLER = "abnormalAttendanceReminderHandler";

        /**
         * 异常考勤-员工每日提醒
         */
        public static final String ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER = "abnormalAttendanceDayRemindHandler";

        /**
         * 加班单调休假发放
         */
        public static final String ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER = "attendanceOTLeavePaymentHandler";

        /**
         * 重新生成用户系统账号名称
         */
        public static final String REFRESH_SYS_ACCOUNT_NAME_HANDLER = "refreshSysAccountNameHandler";

        /**
         * 同步员工信息到中控考勤机
         */
        public static final String SYNC_EMPLOYEE_ZKTECO_HANDLER = "syncEmployeeHandler";
        /**
         * 同步区域和部门信息到中控考勤机
         */
        public static final String SYNC_AREA_DEPT_ZKTECO_HANDLER = "syncAreaAndDeptHandler";
        /**
         * 同步中控考勤机打卡信息
         */
        public static final String SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER = "syncEmployeeAttendanceHandler";

        /**
         * HR编辑同步数据到HERMES
         */
        public static final String USER_EDIT_SYNC_HERMES_HANDLER = "userEditSyncHermesHandler";

        /**
         * 员工考勤日历配置数据转换
         */
        public static final String EMPLOYEE_ATTENDANCE_CONFIG_CONVERT_HANDLER = "employeeAttendanceConfigConvertHandler";

        /**
         * 考勤日历配置数据转换
         */
        public static final String ATTENDANCE_CONFIG_CONVERT_HANDLER = "attendanceConfigConvertHandler";
        /**
         * 用户支付信息同步
         */
        public static final String USER_PAY_INFO_MIGRATE_HANDLER = "userPayInfoMigrateHandler";

        /**
         * 同步tms司机排班数据和司机考核信息数据至司机绩效考核信息表
         */
        public static final String DRIVER_PERFORMANCE_INFO_SYNCHRONOUS_HANDLER = "driverPerformanceInfoSynchronousHandler";

        /**
         * 非司机员工每日打卡统计数据
         */
        public static final String SYNC_EMPLOYEE_ATTENDANCE_STATISTICAL_HANDLER = "syncEmployeeAttendanceStatisticalHandler";

        /**
         * 员工排班初始化
         */
        public static final String SYNC_EMPLOYEE_SCHEDULING_HANDLER = "employeeSchedulingHandler";

        /**
         * 定时任务针对OFF天数进行报警
         */
        public static final String PUNCH_OFF_HANDLER = "punchOffHandler";

        /**
         * 排班记录表弹性时间刷新
         */
        public static final String PUNCH_RECORD_ELASTIC_HANDLER = "punchRecordElasticHandler";

        /**
         * 员工打卡规则初始化
         */
        public static final String INIT_USER_PUNCH_CONFIG_HANDLER = "initUserPunchConfigHandler";

        /**
         * 异常考勤出勤时长数据刷新
         */
        public static final String ABNORMAL_PUNCH_ATTENDANCE_HOURS_REFRESH = "abnormalPunchAttendanceHoursRefresh";

        /**
         * 初始化员工种类
         */
        public static final String INIT_USER_STAFF_HSNDLER = "initUserStaffTypeHandler";

        /**
         * 最早打卡时间刷新
         */
        public static final String EARLIEST_PUNCH_IN_TIME_HANDLER = "earliestPunchInTimeHandler";

        /**
         * 下班时间刷新
         */
        public static final String PUNCH_OUT_TIME_HANDLER = "punchOutTimeHandler";

        /**
         * 非模板部门同步至TMS
         */
        public static final String SYNC_IS_NOT_TEMPLATE_DEPT_HANDLER = "syncIsNotTemplateDeptHandler";

        /**
         * 初始化userRecord
         */
        public static final String USER_RECORD_INIT_HANDLER = "userRecordInitHandler";

        /**
         * 司机等级
         */
        public static final String DRIVER_LEVEL_HANDLER = "driverLevelHandler";

        /**
         * 司机等级初始化
         */
        public static final String DRIVER_LEVEL_INIT_HANDLER = "driverLevelInitHandler";

        /**
         * 默认司机配置规则生成
         */
        public static final String DEFAULT_DRIVER_LEVEL_RULE_INIT_HANDLER = "defaultDriverLevelRuleInitHandler";

        /**
         * 司机等级规则生成对应用户等级
         */
        public static final String DRIVER_CONFIG_INIT_HANDLER = "driverConfigInitHandler";

        /**
         * 定时任务，每日执行，根据规格生成对应的司机等级
         */
        public static final String DRIVER_LEVEL_AUTO_INIT_HANDLER = "driverLevelAutoInitHandler";

        /**
         * 定时任务获取exception da的司机等级的司机
         */
        public static final String DRIVER_LEVEL_REDIS_INIT_HANDLER = "driverLevelRedisInitHandler";

        /**
         * 旧司机等级数据初始化
         */
        public static final String DRIVER_OLD_LEVEL_REFRESH_HANDLER = "driverOldLevelRefreshHandler";

        /**
         * 同步用户至金蝶
         */
        public static final String USER_SYNC_KINGDEE_HANDLER = "userSyncKingdeeHandler";

        /**
         * 同步部门至金蝶
         */
        public static final String DEPT_SYNC_KINGDEE_HANDLER = "deptSyncKingdeeHandler";

        /**
         * 同步部分部门至金蝶
         */
        public static final String PART_DEPT_SYNC_KINGDEE_HANDLER = "partDeptSyncKingdeeHandler";

        /**
         * 同步缺失组织部门至金蝶
         */
        public static final String DEPT_SETTLEMENT_PART_SYNC_HANDLER = "deptSettlementPartSyncHandler";

        /**
         * 定时任务推送网点部门前一天打卡信息
         */
        public static final String PUNCH_STATISTICAL_SEND_HANDLER = "punchStatisticalHandler";

        /**
         * 每天在职员工非司机排班打卡信息入库
         */
        public static final String EMPLOYEE_PUNCH_INFO_HANDLER = "employeePunchInfoHandler";

        /**
         * 每日更新前日考勤看板异常员工数
         */
        public static final String EMPLOYEE_ABNORMAL_DASHBOARD_HANDLER = "employeeAbnormalDashboardHandler";

        /**
         * 定时任务推送前一周在职员工非司机排班打卡信息
         */
        public static final String EMPLOYEE_PUNCH_STATISTIC_HANDLER = "employeePunchStatisticHandler";

        public static final String EMPLOYEE_NO_PUNCH_STATISTIC_HANDLER = "employeeNoPunchStatisticHandler";

        /**
         * 司机派件信息、未出勤司机名单 落库
         */
        public static final String VACANT_DRIVER_MONITOR_HANDLER = "vacantDriverMonitorHandler";

        /**
         * 空置车辆信息监控，落库
         */
        public static final String VACANT_VEHICLE_MONITOR_HANDLER = "vacantVehicleMonitorHandler";

        /**
         * 车辆信息监控、司机出勤监控 推送
         */
        public static final String VEHICLE_MONITOR_PUSH_HANDLER = "vehicleMonitorPushHandler";

        /**
         * KSA车辆信息监控、司机出勤监控 推送
         */
        public static final String KSA_VEHICLE_MONITOR_PUSH_HANDLER = "ksaVehicleMonitorPushHandler";

        /**
         * KSA人员监控 推送
         */
        public static final String KSA_STATION_EMPLOYEE_MONITOR_HANDLER = "KsaStationEmployeeMonitorHandler";
        /**
         * 初始化同步HR工资卡到OA
         */
        public static final String USER_PAYMENT_INIT_HANDLER = "userPaymentInitHandler";

        /**
         * 更新abnormal相关表数据
         */
        public static final String UPDATE_TABLE_DATA_HANDLER = "updateTableDataHandler";

        /**
         * 部门信息同步企业微信
         */
        public static final String SYNC_WECHAT_DEPT_HANDLER = "syncWechatDeptHandler";

        /**
         * 人员信息同步企业微信
         */
        public static final String SYNC_WECHAT_USER_HANDLER = "syncWechatUserHandler";

        /**
         * 删除企业微信测试数据
         */
        public static final String DELETE_WECHAT_TEST_HANDLER = "deleteWechatTestHandler";

        /**
         * 已有企业微信员工 相关表数据更新
         */
        public static final String USER_WECHAT_INIT_HANDLER = "userWechatInitHandler";

        /**
         * 创建企业微信测试部门数据
         */
        public static final String CREATE_PART_DEPT_HANDLER = "createPartDeptHandler";

        /**
         * 创建企业微信测试部门数据
         */
        public static final String CHECK_REPEAT_PUNCH_CONFIG_HANDLER = "checkRepeatPunchConfighandler";


        /**
         * 油费风控看板(网点级别)
         */
        public static final String VEHICLE_STATION_FUEL_DASHBOARD_HANDLER = "vehicleStationFuelDashboardHandler";

        /**
         * 油费风控看板(车辆级别)
         */
        public static final String VEHICLE_FUEL_DASHBOARD_HANDLER = "vehicleFuelDashboardHandler";

        /**
         * 差旅费目的国同步
         */
        public static final String DESTINATION_COUNTRY_SYNCHRONIZATION = "DestinationCountrySynchronization";

        /**
         * 差旅费规定日期更新
         */
        public static final String UPDATE_TRAVEL_REGULATION = "UpdatedTravelRegulationsHandler";

        /**
         * 绩效考核活动开启
         */
        public static final String ACHIEVEMENT_START_EXAMINE = "ACHIEVEMENTSTARTEXAMINE";

        /**
         * 员工绩效执行人变更
         */
        public static final String ACHIEVEMENT_EXECUTOR_MAINTENANCE = "AchievementExecutorMaintenance";

        /**
         * 员工绩效状态变更
         */
        public static final String ACHIEVEMENT_STATUS_CHANGE = "AchievementStatusChange";

        /**
         * 员工绩效状态变更至最终节点
         */
        public static final String ACHIEVEMENT_STATUS_CHANGE_TO_FINAL = "AchievementStatusChangeToFinal";


        /**
         * 绩效线上数据修正
         */
        public static final String ACHIEVEMENT_ONLINE_DATA_CORRECTION = "ACHIEVEMENTONLINEDATACORRECTION";


        /**
         * 同步部门code至hermes
         */
        public static final String SYNC_USER_DEPT_CODE_HANDLER = "syncUserDeptCodeHandler";

        /**
         * 更新黑名单封禁状态
         */
        public static final String UPDATE_BAN_STATUS_HANDLER = "updateBanStatusHandler";

        /**
         * 定时刷新黑名单缓存
         */
        public static final String REFRESH_BLACKLIST_CACHE_HANDLER = "RefreshBlacklistCacheHandler";

        /**
         * 固定班次自动排班续期任务
         */
        public static final String FIXED_CLASS_AUTO_SCHEDULING_HANDLER = "fixedClassAutoSchedulingHandler";

        /**
         * 周期排班自动续上周期
         */
        public static final String CYCLE_SHIFT_HANDLER = "cycleShiftHandler";

        /**
         * 同步部门信息到Waukeen
         */
        public static final String SYNC_WAUKEEN_DEPT_INFO_HANDLER = "syncWaukeenDeptInfoHandler";

        /**
         * 打印员工职级
         */
        public static final String USER_JOB_LEVEL_HANDLER = "userJobLevelHandler";

        /**
         * 删除已离职人员的企业微信
         */
        public static final String DELETE_WECHAT_HANDLER = "deleteWechatHandler";

        /**
         * 用户考勤周期补卡次数配置
         */
        public static final String USER_REISSUE_CARD_CONFIG_HANDLER = "userReissueCardConfigHandler";

        /**
         * 用户正常考勤表小时初始化为分钟
         */
        public static final String ATTENDANCE_DETAIL_MINUTE_INIT_HANDLER = "attendanceDetailMinuteInitHandler";

        /**
         * 用户假期余额转换到分钟初始化
         */
        public static final String USER_LEAVE_MINUTES_INIT_HANDLER = "userLeaveMinutesInitHandler";

        /**
         * 用户考勤单据确认周期监控
         */
        public static final String APPROVAL_FORM_CONFIRM_CYCLE_HANDLER = "approvalFormConfirmCycleHandler";

        /**
         * 异常考勤确认周期监控
         */
        public static final String ABNORMAL_CONFIRM_CYCLE_HANDLER = "abnormalConfirmCycleHandler";

        /**
         * 用户打卡规则/日历范围检查
         */
        public static final String ATTENDANCE_PUNCH_CONFIG_RANGE_CHECK_HANDLER = "attendancePunchConfigRangeCheckHandler";

        /**
         * 用户打卡规则/日历错误数据检查,不修复(检查用户是默认级别的，但所属部门存在指定日历/打卡规则  用户是部门级的，但部门没有绑定任何日历/打卡规则)
         */
        public static final String ATTENDANCE_PUNCH_CONFIG_RANGE_ERROR_HANDLER = "attendancePunchConfigRangeErrorHandler";

        /**
         * 薪资考勤报表自动成成
         */
        public static final String SALARY_ATTENDANCE_USER_DETAIL_HANDLER = "salaryAttendanceUserDetailHandler";

        /**
         * 薪资结算员工代办单据生成
         */
        public static final String SALARY_SETTLEMENT_AGENT_RECORD_HANDLER = "salarySettlementAgentRecordHandler";

        /**
         * 薪资结算员工生成
         */
        public static final String SALARY_SETTLEMENT_USER_INFO_HANDLER = "salarySettlementUserInfoHandler";

        /**
         * 薪资结算员工生成(初始化)
         */
        public static final String SALARY_SETTLEMENT_USER_INIT_HANDLER = "salarySettlementUserInitHandler";

        /**
         * 薪资结算员工提报明细生成(初始化)
         */
        public static final String SALARY_SETTLEMENT_USER_DETAIL_INIT_HANDLER = "salarySettlementUserDetailInitHandler";

        /**
         * 薪资结算审批单通过入口
         */
        public static final String SALARY_SETTLEMENT_FORM_PASS_HANDLER = "salarySettlementFormPassHandler";

        /**
         * 代办记录提示
         */
        public static final String SALARY_SETTLEMENT_AGENT_NOTICE_HANDLER = "salarySettlementAgentNoticeHandler";

        /**
         * 用户薪资也计薪方案时间解耦旧数据刷新
         */
        public static final String SALARY_USER_EFFECT_TIME_REFRESH_HANDLER = "salaryUserEffectTimeRefreshHandler";

        /**
         * 薪资报表配置自动生成
         */
        public static final String SALARY_REPORT_CONFIG_HANDLER = "salaryReportConfigHandler";

        /**
         * 薪资计薪国配置从TMS获取基础信息
         */
        public static final String SALARY_PAYMENT_COUNTRY_CONFIG_HANDLER = "salaryPaymentCountryConfigHandler";


        /**
         * 薪资报表配置填充常住地
         */
        public static final String SALARY_REPORT_RESIDENT_LOCATION_HANDLER = "SalaryReportResidentLocationHandler";

        /**
         * 薪资增减项迁移
         */
        public static final String SALARY_ITEM_TRANSFER_HANDLER = "salaryItemTransferHandler";


        /**
         * 薪资增减项迁移(费用信息)
         */
        public static final String SALARY_ITEM_EXPENSE_TRANSFER_HANDLER = "salaryItemExpenseTransferHandler";

        /**
         * 旧的打卡规则生成法定工作时长/休息时长
         */
        public static final String PUNCH_REST_INIT_HANDLER = "punchRestInitHandler";


        /**
         * 司机税务ID同步
         */
        public static final String DRIVER_TAX_ID_SYNC_HANDLER = "driverTaxIdSyncHandler";

        /**
         * 用户导入基本工资初始化
         */
        public static final String SALARY_USER_AMOUNT_INIT_HANDLER = "salaryUserAmountInitHandler";

        public static final String SYNC_WECOM_USER_HANDLER = "syncWecomUserHandler";

        public static final String SALARY_COST_DEPT_INIT_HANDLER = "salaryCostDeptInitHandler";

        public static final String SALARY_COST_PROJECT_INIT_HANDLER = "SalaryCostProjectInitHandler";

        /**
         * 组织同步
         */
        public static final String DEPT_SYNC_HANDLER = "deptSyncHandler";
        /**
         * 组织层级重置
         */
        public static final String DEPT_LEVEL_RESET_HANDLER = "deptLevelResetHandler";

        /**
         * 薪资消息提示周期处理
         */
        public static final String SALARY_MESSAGE_NOTICE_HANDLER = "salaryMessageNoticeHandler";


        /**
         * 企业微信员工数据同步
         */
        public static final String WECHAT_USER_INFO_TO_UCENTER_SYNC_HANDLER = "wechatUserInfoToUcenterSyncHandler";

        /**
         * 人员同步
         */
        public static final String USER_SYNC_HANDLER = "userSyncHandler";

        /**
         * 清理企业微信废弃部门
         */
        public static final String WECOM_DEPT_CLEAN_HANDLER = "wecomDeptCleanHandler";

        /**
         * 薪资计算任务生成
         */
        public static final String SALARY_CALCULATE_TASK_HANDLER = "salaryCalculateTaskHandler";

        public static final String SYN_TMS_2_HRMS_ACCOUNT = "SynTms2HrmsAccount";

        public static final String TMS_USER_2_HRMS_SYNC_HANDLER = "tmsUser2HrmsSyncHandler";

        /**
         * 生成时间窗口配置（全球国家）
         */
        public static final String SALARY_TASK_CONFIG_HANDLER = "salaryTaskConfigHandler";

        public static final String SEND_WECHAT_MESSAGE_HANDLER = "sendWechatMessageHandler";

        /**
         * 第三方数据同步：资产系统全量同步部门/公司
         */
        public static final String ASSET_SYNC_DEPT_COMPANY_HANDLER = "assetSyncDeptHandler";

        /**
         * 部门快照
         */
        public static final String DEPT_SNAPSHOT_HANDLER = "deptSnapshotHandler";

        /**
         * 部门生效管理
         */
        public static final String DEPT_ACTIVE_TASK_HANDLER = "deptActiveTaskHandler";

        /**
         * 对【试用考核中】员工更改状态至【待配置答辩】
         */
        public static final String PROBATION_CONFIG_DEFENSE_HANDLER = "probationConfigDefenseHandler";

        /**
         * 对【待转正】员工更改状态至【已转正】
         */
        public static final String PROBATION_CONFIRMATION_HANDLER = "probationConfirmationHandler";

    }


    public static class HermesDict {
        public static final String PUNCH_RULE_TYPE = "punchRuleType";
    }
}
