package com.imile.attendance.util;

import cn.hutool.core.date.DateUtil;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/20 
 * @Description
 */
public class PunchTimeCalculator {

    /**
     * 计算指定日期的打卡时间，支持前一天或后一天的偏移
     *
     * @param dayDate 基准日期
     * @param timeOnly 只包含时分秒的时间
     * @param dayOffset 日期偏移量: -1表示前一天，0表示当天，1表示后一天
     * @return 完整的日期时间
     */
    public static Date calculatePunchTime(Date dayDate, Date timeOnly, int dayOffset) {
        // 提取时分秒
        String timeString = DateHelper.formatHHMMSS(timeOnly);

        // 根据偏移量调整日期
        Date adjustedDate = dayDate;
        if (dayOffset != 0) {
            adjustedDate = DateUtil.offsetDay(dayDate, dayOffset);
        }

        // 计算日期部分
        String dayString = DateHelper.formatYYYYMMDD(adjustedDate);
        // 合并日期和时间
        return getDateFromDateAndTimeStr(dayString, timeString);
    }

    /**
     * LocalDate用法
     * 性能更好：避免了字符串解析和格式化的开销
     * DateUtil.offsetDay(dayDate, -1),combined.minusDays(1);
     * ZoneId.systemDefault()改为ZoneId.of("Asia/Shanghai")？
     */
    public static Date calculateDateTime(Date dayDate, Date timeOnly, boolean isAcross) {
        LocalDateTime datePart = dayDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atStartOfDay();
        LocalTime timePart = timeOnly.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
        LocalDateTime combined = datePart.with(timePart);

        if (isAcross) {
            combined = combined.minusDays(1);
        }

        return Date.from(combined.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断是否跨天
     * 当最早时间大于等于标准时间时，表示跨天
     */
    public static boolean isTimeAcrossDay(Date earliestTime, Date standardTime) {
        return earliestTime.compareTo(standardTime) >= 0;
    }

    public static Date getDateFromDateAndTimeStr(String dateStr, String timeStr) {
        String fullDateTimeStr = DateHelper.concatDateAndTimeStr(dateStr, timeStr);
        return DateHelper.parseYYYYMMDDHHMMSS(fullDateTimeStr);
    }

    /**
     * 根据基准日期和时间计算完整日期时间
     * @param baseDate 基准日期
     * @param timeOnly 时间部分
     * @param dayOffset 天数偏移量
     * @return 完整日期时间
     */
    public static Date calculateFullDateTime(Date baseDate, Date timeOnly, int dayOffset) {
        Date baseWithOffset = dayOffset == 0 ? baseDate : DateUtil.offsetDay(baseDate, dayOffset);
        return getDateFromDateAndTime(baseWithOffset, timeOnly);
    }


    public static Date getDateFromDateAndTime(Date date, Date time) {
        return getDateFromDateAndTimeStr(
                DateHelper.formatYYYYMMDD(date),
                DateHelper.formatHHMMSS(time)
        );
    }

    /**
     * 将时间字符串转换为标准时间对象，用于比较
     */
    public static Date convertToStandardTimeFormat(Date time) {
        String timeString = DateHelper.formatHHMMSS(time);
        return DateHelper.parseYYYYMMDDHHMMSS(
                DateHelper.appendDefaultDateStrToTimeStr(timeString));
    }
}
