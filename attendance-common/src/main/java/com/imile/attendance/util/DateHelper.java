package com.imile.attendance.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.MonthEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;

/**
 * 日期计算工具类
 *
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
public class DateHelper {

    private static final String HOUR_MIN = "HH:mm";

    private static final double HOUR = 3600 * 1000d;

    private static final double MINUS = 60 * 1000d;


    /**
     * yyyy/MM/dd 格式的日期格式化器
     */
    private static final String PATTERN_DATE_SLASH = "yyyy/MM/dd";

    /**
     * yyyy-MM-dd HH:mm:ss 格式的日期格式化器
     */
    public static final DateTimeFormatter NORM_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    public static String getJVMDefaultZoneId() {
        TimeZone systemTimeZone = TimeZone.getDefault();
        return systemTimeZone.getID();
    }

    /**
     * 获得年的部分
     *
     * @param date 日期
     * @return 年的部分
     */
    public static int year(Date date) {
        return DateUtil.year(date);
    }

    /**
     * 获得月份，从1开始计数
     *
     * @param date 日期
     * @return 月份，从1开始计数
     */
    public static int month(Date date) {
        return DateUtil.month(date) + 1;
    }

    /**
     * 获得指定日期是这个日期所在月份的第几天
     *
     * @param date 日期
     * @return 天
     */
    public static int dayOfMonth(Date date) {
        return DateUtil.dayOfMonth(date);
    }

    /**
     * 获取格式化的月份字符串（01-12）
     *
     * @param date 日期
     * @return 两位数的月份字符串
     */
    public static String formatMonth(Date date) {
        int month = month(date);
        return month < 10 ? "0" + month : String.valueOf(month);
    }

    /**
     * 将日期转为dayId,格式为yyyyMMdd
     */
    public static long getDayId(Date date) {
        // yyyyMMdd
        return Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(Long dayId) {
        if (dayId == null) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(String dayId) {
        if (StringUtils.isEmpty(dayId)) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId, DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 获取偏移指定天数后的dayId
     *
     * @param dayId  当前dayId (格式：yyyyMMdd)
     * @param offset 偏移天数，正数表示后移，负数表示前移
     * @return 偏移后的dayId
     */
    public static Long getOffsetDayId(Long dayId, int offset) {
        Date date = transferDayIdToDate(dayId);
        Date offsetDay = DateUtil.offsetDay(date, offset);
        return getDayId(offsetDay);
    }

    /**
     * 获取后一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 后一天的dayId
     */
    public static Long getNextDayId(Long dayId) {
        return getOffsetDayId(dayId, 1);
    }

    /**
     * 获取前一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 前一天的dayId
     */
    public static Long getPreviousDayId(Long dayId) {
        return getOffsetDayId(dayId, -1);
    }

    /**
     * dayId格式转换为 15-Nov-22格式 20220101
     */
    public static String dayIdFormat(Long dayId) {
        String day = dayId.toString().substring(6);
        String monthInt = dayId.toString().substring(4, 6);
        MonthEnum instance = MonthEnum.getInstance(monthInt);
        String month = instance.getDesc();
        String year = dayId.toString().substring(0, 4);
        return day + "-" + month + "-" + year;
    }

    /**
     * 获取从startDayId到endDayId之间的所有dayId列表（包含开始和结束日期）
     *
     * @param startDayId 开始日期的dayId，格式为yyyyMMdd
     * @param endDayId   结束日期的dayId，格式为yyyyMMdd
     * @return 包含所有日期dayId的列表
     */
    public static List<Long> getDayIdList(long startDayId, long endDayId) {
        if (startDayId > endDayId) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        List<Long> dayIdList = new ArrayList<>();

        // 当前日期
        Date currentDate = transferDayIdToDate(startDayId);

        // 循环直到超过endDayId
        while (true) {
            long currentDayId = getDayId(currentDate);
            dayIdList.add(currentDayId);

            // 如果已经到达结束日期，则退出循环
            if (currentDayId >= endDayId) {
                break;
            }

            // 日期加1天
            currentDate = DateUtil.offsetDay(currentDate, 1);
        }

        return dayIdList;
    }

    /**
     * 将打卡时间添加默认日期前缀
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间字符串（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回空字符串
     */
    public static String appendDefaultDateStrToTimeStr(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            // 输入为空时返回空字符串
            return "";
        }
        // 添加默认日期前缀：1970-01-01
        return BusinessConstant.DEFAULT_TIME + timeStr;
    }

    /**
     * 将打卡时间添加默认日期前缀(DatePattern.NORM_DATETIME_PATTERN)
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回null
     */
    public static Date appendDefaultDateToTime(String timeStr) {
        String defaultDateToTimeStr = appendDefaultDateStrToTimeStr(timeStr);
        if (StringUtils.isBlank(defaultDateToTimeStr)) {
            return null;
        }
        return DateUtil.parse(defaultDateToTimeStr);
    }

    /**
     * 判断两个时间是否跨天
     *
     * @param firstTime  进入时间
     * @param secondTime 退出时间
     * @return 如果时间跨天返回 BusinessConstant.Y（代表“是”），否则返回 BusinessConstant.N（代表“否”）
     */
    public static Integer judgeCrossDay(Date firstTime, Date secondTime) {
        if (firstTime.before(secondTime)) {
            return BusinessConstant.N;
        }
        return BusinessConstant.Y;
    }

    /**
     * 将日期格式化为yyyyMMdd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatPureDate(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.PURE_DATE_PATTERN) : "";
    }

    /**
     * 将时间格式化为 HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的时间字符串
     */
    public static String formatHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_TIME_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDD(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDDHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN) : "";
    }

    /**
     * 连接日期和时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateStr 日期字符串 yyyy-MM-dd
     * @param timeStr 时间字符串 HH:mm:ss
     * @return 连接后的日期时间字符串
     */
    public static String concatDateAndTimeStr(String dateStr, String timeStr) {
        return dateStr + " " + timeStr;
    }

    /**
     * 连接日期和时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateStr 日期字符串 yyyy-MM-dd
     * @param timeStr 时间字符串 HH:mm:ss
     * @return 连接后的日期时间
     */
    public static Date concatDateAndTime(String dateStr, String timeStr) {
        return parseYYYYMMDDHHMMSS(dateStr + " " + timeStr);
    }

    /**
     * 将日期字符串转换为日期
     *
     * @param dateStr 日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 对应的日期
     */
    public static Date parseYYYYMMDDHHMMSS(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
    }

    public static Date parseYYYYMMDD(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN);
    }

    /**
     * 将日期格式化为 yyyy/MM/dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDateWithSlash(Date date) {
        return date != null ? DateUtil.format(date, PATTERN_DATE_SLASH) : "";
    }

    /**
     * 将 yyyy/MM/dd 格式的字符串转换为日期
     *
     * @param dateStr yyyy/MM/dd 格式的日期字符串
     * @return Date 对象，如果转换失败返回 null
     */
    public static Date parseDateWithSlash(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
    }

    /**
     * 检查字符串是否为有效的 yyyy/MM/dd 格式日期
     *
     * @param dateStr 待检查的日期字符串
     * @return 是否为有效日期
     */
    public static boolean isValidDateWithSlash(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        try {
            DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Date pushDate(Date date, int pushDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date == null ? new Date() : date);
        calendar.add(Calendar.DATE, pushDay);
        return calendar.getTime();
    }

    /**
     * 判断两个日期相差几天
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDateDiff(Date startDate, Date endDate) {
        return (int) ((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
    }

    /**
     * 判断两个日期是否跨年
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 是否跨年
     */
    public static Boolean judgeCrossYear(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        int firstYear = year(startDate);
        int secondYear = year(endDate);
        return firstYear != secondYear;
    }

    /**
     * 判断两个dayId是否跨年（dayId格式：yyyyMMdd）
     *
     * @param startDayId 第一个dayId
     * @param endDayId   第二个dayId
     * @return 是否跨年
     */
    public static Boolean judgeCrossYearByDayId(Long startDayId, Long endDayId) {
        if (startDayId == null || endDayId == null) {
            return false;
        }
        // 提取年份部分（前4位）
        int firstYear = (int) (startDayId / 10000);
        int secondYear = (int) (endDayId / 10000);
        return firstYear != secondYear;
    }

    /**
     * 根据时区转换日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date     日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone()
                    .getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0) {
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone()
                        .getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * 将日期字符串转换为对应时区的日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param dateStr  日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 返回对应时区的日期
     */
    public static Date convertDateStrByTimeZonePlus(String timeZone, String dateStr) {
        return convertDateByTimeZonePlus(timeZone,
                DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN));
    }

    public static BigDecimal diffMins(Date var1, Date var2) {
        if (var1 == null || var2 == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.setDoubleScale((var1.getTime() - var2.getTime()) / MINUS, 2);
    }

    public static Date beginOfDay(Date date) {
        if (Objects.isNull(date)){
            return new Date();
        }
        return DateUtil.beginOfDay(date);
    }

    public static Date endOfDay(Date date) {
        if (Objects.isNull(date)){
            return new Date();
        }
        return DateUtil.endOfDay(date);
    }

    public static int getYearFormDayId(Long dayId){
        if (null == dayId){
            return 0;
        }
        return Integer.parseInt(Long.toString(dayId).substring(0, 4));
    }

    /**
     * 将毫秒级时间戳转换为指定时区下 "yyyy-MM-dd HH:mm:ss" 格式的字符串。
     * 支持多种时区格式：
     * - 带加号前缀：如 "+8", "+10"
     * - 纯数字：如 "8", "9", "10"
     * - 带减号前缀：如 "-8", "-4"
     *
     * @param timestampMillis 毫秒级的时间戳
     * @param zoneIdStr    目标时区的ID
     * @return 格式化后的日期时间字符串
     */
    public static String convertTimestampToDateStr(long timestampMillis, String zoneIdStr) {
        if (timestampMillis <= 0) {
            return "Invalid Timestamp";
        }
        Instant instant = Instant.ofEpochMilli(timestampMillis);
        ZoneId zoneId;
        try {
            // 处理不同格式的时区字符串
            String formattedZoneId;
            if (zoneIdStr.startsWith("+") || zoneIdStr.startsWith("-")) {
                // 已经带有符号的情况，直接使用
                formattedZoneId = "GMT" + zoneIdStr;
            } else {
                // 纯数字情况，视为正时区，添加 "+"
                formattedZoneId = "GMT+" + zoneIdStr;
            }
            zoneId = ZoneId.of(formattedZoneId);
        } catch (Exception e) {
            log.warn("无效的时区ID: {}, 将使用系统默认时区。", zoneIdStr);
            zoneId = ZoneId.systemDefault();
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        return localDateTime.format(NORM_DATETIME_FORMATTER);
    }

    /**
     * 将特定时区的日期时间字符串转换为毫秒级 Unix 时间戳。
     * 支持多种时区格式：
     * - 带加号前缀：如 "+8", "+10"
     * - 纯数字：如 "8", "9", "10"
     * - 带减号前缀：如 "-8", "-4"
     *
     * @param dateStr 日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @param timezoneId     时区ID
     * @return 毫秒级的 Unix 时间戳
     */
    public static long convertDateStrToTimestamp(String dateStr, String timezoneId) {
        if (StringUtils.isEmpty(dateStr)) {
            log.warn("日期时间字符串为空");
            return 0L;
        }

        try {
            // 解析日期时间字符串
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, NORM_DATETIME_FORMATTER);

            // 处理不同格式的时区字符串
            String formattedZoneId;
            if (timezoneId.startsWith("+") || timezoneId.startsWith("-")) {
                // 已经带有符号的情况，直接使用
                formattedZoneId = "GMT" + timezoneId;
            } else {
                // 纯数字情况，视为正时区，添加 "+"
                formattedZoneId = "GMT+" + timezoneId;
            }

            ZoneId zoneId;
            try {
                zoneId = ZoneId.of(formattedZoneId);
            } catch (Exception e) {
                log.warn("无效的时区ID: {}, 将使用系统默认时区。", timezoneId);
                zoneId = ZoneId.systemDefault();
            }

            // 将本地日期时间与时区关联
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

            // 转换为 Instant 并获取毫秒时间戳
            return zonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.warn("日期时间字符串解析失败: {}", dateStr, e);
            return 0L;
        }
    }

    /**
     * 忽略时间中的秒和毫秒部分
     * 例如：将 "2023-10-05 14:30:45" 转换为 "2023-10-05 14:30:00"
     *
     * @param time 原始时间
     * @return 忽略秒和毫秒后的时间，如果输入为null则返回null
     */
    public static Date truncateToMinute(Date time) {
        if (time == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.SECOND, 0);
        // 同时清零毫秒
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static void main(String[] args) {
//        System.out.println(convertTimestampToString(1747883423082L, "8"));
//        System.out.println(convertTimestampToString(1747883423082L, "10"));
//        System.out.println(convertTimestampToString(1747883423082L, "+11"));
//        System.out.println(convertTimestampToString(1747883423082L, "-8"));
//
//        System.out.println(DateHelper.formatHHMMSS(new Date()));

        // 测试 convertStringToTimestamp 方法
        String testDateTime = "2025-05-22 14:00:00";
        System.out.println("原始日期时间字符串: " + testDateTime);
        long timestamp = convertDateStrToTimestamp(testDateTime, "8");
        System.out.println("转换为时间戳: " + timestamp);
        System.out.println("时间戳转回字符串: " + convertTimestampToDateStr(timestamp, "8"));

        // 测试不同时区
        System.out.println("GMT+10 时间戳: " + convertDateStrToTimestamp(testDateTime, "10") +
                " GMT+10 日期时间字符串: " + convertTimestampToDateStr(timestamp, "10"));
        System.out.println("GMT-8 时间戳: " + convertDateStrToTimestamp(testDateTime, "-8") +
                " GMT-8 日期时间字符串: " + convertTimestampToDateStr(timestamp, "-8"));

        TimeZone systemTimeZone = TimeZone.getDefault();
        System.out.println(systemTimeZone.getID());
    }
}
