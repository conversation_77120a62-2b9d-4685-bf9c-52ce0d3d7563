package com.imile.attendance.util;

import com.imile.attendance.dto.DateAndTimeZoneDate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Slf4j
public class CommonUtil {

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date
     * @return Date
     */
    public static Date convertDateByTimeZone(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date 日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0) {
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone().getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * 根据指定的时区和日期创建 DateAndTimeZoneDate 对象
     * 该方法将原始日期和根据时区转换后的日期封装到 DateAndTimeZoneDate 对象中
     *
     * @param timeZone 时区值，例如：8 表示北京时间，3 表示沙特时间，4 表示迪拜时间，负数表示西半球时区
     * @param date 需要转换的原始日期
     * @return 包含原始日期和时区转换后日期的 DateAndTimeZoneDate 对象
     */
    public static DateAndTimeZoneDate getDateAndTimeZoneDate(String timeZone, Date date) {
        Date convertDate = convertDateByTimeZonePlus(timeZone, date);
        return DateAndTimeZoneDate.of(date, convertDate);
    }


    public static void main(String[] args) {
        Date date = new Date();
//        Date date10 = convertDateByTimeZonePlus("10", date);
//        Date date12 = convertDateByTimeZonePlus("12", date);
//        Date dateN6 = convertDateByTimeZonePlus("-6", date);
//        Date dateN8 = convertDateByTimeZonePlus("-8", date);
//        System.out.println(date+":"+date.getTime());
//        System.out.println(date10+":"+date10.getTime());
//        System.out.println(date12+":"+date12.getTime());
//        System.out.println(dateN6+":"+dateN6.getTime());
//        System.out.println(dateN8+":"+dateN8.getTime());


        DateAndTimeZoneDate date10 = getDateAndTimeZoneDate("10", date);
        DateAndTimeZoneDate date12 = getDateAndTimeZoneDate("12", date);
        DateAndTimeZoneDate dateN6 = getDateAndTimeZoneDate("-6", date);
        DateAndTimeZoneDate dateN8 = getDateAndTimeZoneDate("-8", date);
        System.out.println(date + ":" + date.getTime());
        System.out.println(date10.getTimeZoneDate() + ":" + date10.getDateTimeStamp());
        System.out.println(date12.getTimeZoneDate() + ":" + date12.getDateTimeStamp());
        System.out.println(dateN6.getTimeZoneDate() + ":" + dateN6.getDateTimeStamp());
        System.out.println(dateN8.getTimeZoneDate() + ":" + dateN8.getDateTimeStamp());
    }
}
