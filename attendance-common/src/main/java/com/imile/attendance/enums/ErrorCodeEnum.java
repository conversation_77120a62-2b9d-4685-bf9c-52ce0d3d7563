package com.imile.attendance.enums;

import com.imile.attendance.util.BizExceptionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15
 * @Description
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum implements BaseErrorMsg {

    SUCCESS("200", "success", "操作成功"),
    REQUEST_HANDLE_FAIL("400", "request.handle.fail", "请求处理失败"),
    NO_ACCESS("401", "no.access", "无权限"),
    PARAM_VALIDATE_FAIL("402", "param.validate.fail", "参数校验失败"),
    SERVICE_HANDLE_EXCEPTION("500", "system.processing.exception", "服务处理异常"),
    NETWORK_EXCEPTION("505", "Network.exception", "网络出现异常"),


    DATA_DUPLICATE("20001", "data.duplicate", "数据重复"),
    PARAM_VALID_ERROR("20002", "exception.paramValidError", "参数错误"),
    DATA_NOT_EXITS("20003", "data.not.exist", "数据不存在"),

    /**
     * 没有系统登录权限，此处的code暂时没用
     */
    NO_LOGIN_AUTH_ERROR("20004", "none.system.login.permission", "没有系统登录权限，请联系管理员进行授权"),
    /**
     * 没有访问权限
     */
    NO_ACCESS_AUTH_ERROR("20005", "no.access.auth", "没有数据访问权限"),

    /**
     * 日志记录
     */
    LOG_RECORD_MODULE_NOT_EXIST_OR_NOT_SUPPROT("40001", "log.record.module.not.exist", "数据不存在"),

    REPEAT_SUBMIT("50001", "repeat.submit", "处理中"),
    ACCOUNT_NOT_EXITS("50006", "account.not.exist", "用户不存在"),

    COUNTRY_CODE_NOT_EXISTS("50016", "country.not.exists", "国家不存在"),
    ACCOUNT_REPEAT_HANDLER_ERROR("50019", "account.repeat.handler.error", "该账号正在处理中，请稍候"),

    DEPT_NOT_EXITS("50009", "dept.not.exist", "部门不存在"),
    POST_NOT_EXITS("50010", "post.not.exist", "岗位不存在"),
    PARAM_NOT_NULL("50112", "param.not.null", "参数不能为空"),

    EMPLOYEE_TYPE_ERROR("50114", "employee.type.error", "用工类型错误"),

    /**
     * 附件相关异常
     */
    COUNT_LIMIT_ERROR("50301", "count.limit.error", "附件上传个数超过上限"),
    SIZE_LIMIT_ERROR("50302", "size.limit.error", "附件大小超过上限"),
    ATTACHMENT_NOT_EMPTY("50303", "attachment.not.empty", "附件不能为空"),


    USER_NOT_HAVE_CALENDAR("50404", "user.not.have.calendar", "该用户没有日历方案，请先创建日历方案"),

    DATE_FORMAT_ERROR("50410", "date.format.error", "日期格式错误"),

    ZKTECO_AREA_AND_TERMINAL_SN_NOT_EXIST("50480", "zkteco.area.and.terminal.sn.not.exist", "中控区域和考勤机不存在映射数据"),
    PUNCH_CARD_VERSION_ERROR("50481", "punch.card.version.behind.please.refresh.in.time", "打卡版本落后,请及时刷新"),
    EXPORT_TIME_RANGE_CANNOT_EXCEED_ONE_MONTH("50491", "export.time.range.cannot.exceed.one.month", "导出时间范围不可超过一个月"),
    WIFI_MAC_ADDRESS_FORMAT_ERROR("50496", "Please.enter.the.correct.MAC.address", "请输入正确MAC地址 格式如 02:10:18:02:40:7b"),
    WIFI_MAC_ADDRESS_DUPLICATE_ERROR("50497", "mac.address.already.exists", "mac地址已存在"),
    GPS_ADDRESS_DUPLICATE_ERROR("50499", "gps.address.already.exists", "gps地址已存在"),
    GPS_NAME_DUPLICATE_ERROR("50500", "gps.name.already.exists", "当前国家下gps名称已存在"),

    DEFAULT_CALENDAR_NOT_EXITS("50402", "default.calendar.not.exist", "默认日历方案不存在，请先创建默认日历方案"),
    CUSTOM_ATTENDANCE_NOT_CLOSE("50493", "custom.attendance.not.close", "不可以关闭，此考勤日历现有员工在使用"),
    ATTENDANCE_CALENDAR_NOT_EXIST("50495", "attendance.calendar.not.exist", "该员工所属国家的考勤日历没有配置[{}]年日历，请前往【calendar】配置[{}]年日历"),

    START_TIME_NOT_EMPTY("505003", "startTime.cannot.be.empty", "开始时间不能为空"),
    END_TIME_NOT_EMPTY("505004", "endTime.cannot.be.empty", "结束时间不能为空"),
    START_TIME_CANNOT_BE_LATER_THAN_END_TIME("505005", "startTime.cannot.be.later.than.endTime", "开始时间不能晚于结束时间"),
    TIME_RANGE_LIMITED_DAYS("505006", "time.range.limited.days", "时间筛选范围限制1000天"),

    /**
     * 异常考勤相关
     */
    USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY("50470", "user.not.have.abnormal.attendance.record.on.the.current.day", "异常考勤记录不存在"),
    ABNORMAL_ID_NOT_EMPTY("95016", "abnormal.id.not.empty", "异常考勤ID不能为空"),
    ABNORMAL_HAS_BEEN_HANDLER("95017", "abnormal.has.been.handler", "异常考勤已被处理"),
    ABNORMAL_ONLY_ONE_DAY("95018", "abnormal.only.one.day", "异常考勤只能是同一天"),
    ABNORMAL_IN_REVIEW("95034", "abnormal.in.review", "异常考勤已在审批中"),

    /**
     * 用户假期余额
     */
    THE_HOLIDAY_ADJUSTMENT_IS_NOT_LEGAL("50494", "the.holiday.adjustment.amount.needs.to.be.between.-999.and.999", "假期调整额度需要在-999 到 999 之间"),

    /**
     * 考勤审批
     */
    LEAVE_START_DATE_NOT_EMPTY("95001", "leave.start.date.not.empty", "请假开始时间不能为空"),
    LEAVE_END_DATE_NOT_EMPTY("95002", "leave.end.date.not.empty", "请假结束时间不能为空"),
    EXPECTED_LEAVE_TIME_NOT_EMPTY("95003", "expected.leave.time.not.empty", "预计请假时长不能为空"),
    REMARK_NOT_EMPTY("95004", "remark.not.empty", "备注不能为空"),
    EXIST_CLASH_TIME_PERIOD("95005", "exist.clash.time.period", "存在冲突时间段"),
    THIS_DAY_NOT_HAVE_PUNCH_CONFIG("95006", "this.day.not.have.punch.config", "该天没有设置打卡规则"),
    EXTENDED_LEAVE_PERIOD("95007", "extended.leave.period", "超出请假/外勤/补卡周期"),
    USER_NOT_HAVE_ATTENDANCE_REISSUE_CARD_CONFIG("95008", "user.not.have.attendance.reissue.card.config", "该员工没有设置补卡规则"),
    CORRECT_PUNCH_TIME_NOT_EMPTY("95009", "correct.punch.time.can.not.be.empty", "补卡时间不能为空"),

    APPLICATION_FORM_DOES_NOT_HAVE_VACATION("95010", "application。form.does.not.have.a.corresponding.vacation", "申请单据不存在对应假期"),
    APPLICATION_FORM_IS_EMPTY("95011", "application.form.is.empty", "申请单据不存在"),
    FORM_STATUS_NOT_CANCEL("95012", "form.status.not.cancel", "当前单据状态不允许取消"),
    OUT_OF_OFFICE_START_DATE_NOT_EMPTY("95014", "out.of.office.start.date.not.empty", "外勤开始时间不能为空"),
    OUT_OF_OFFICE_END_DATE_NOT_EMPTY("95015", "out.of.office.end.date.not.empty", "外勤结束时间不能为空"),
    USER_ATTENDANCE_REISSUE_CARD_COUNT_IS_OVER("95021", "user.attendance.reissue.card.count.is.over", "已超出当前考勤周期可补卡上限"),
    ABNORMAL_RECORD_DATE_NOT_EMPTY("95022", "abnormal.record.date.not.empty", "异常考勤日期不能为空"),
    REISSUE_CARD_NOT_HANDLER_THIS_ABNORMAL_TYPE("95026", "reissue.card.not.handler.this.abnormal.type", "补卡不能处理该种异常类型"),
    ATTENDANCE_APPROVAL_BASIC_INFO_EMPTY("95028", "attendance.approval.basic.info.empty", "考勤审批基础信息不存在"),
    REVOKE_FORM_RELATION_FORM_EMPTY("95030", "revoke.form.relation.form.empty", "撤销申请单关联的发起申请单不存在"),
    NOT_REVOKE_REPEAT("95031", "not.revoke.repeat", "该单据已经被撤销，不允许重复操作"),
    ONLY_STAGE_STATUS_CAN_BE_DELETE("95032", "only.stage.status.can.be.delete", "只有暂存状态的单据可以被删除"),

    DEPT_EXIST_IN_OTHER_ATTENDANCE_CONFIG("95035", "dept.exist.in.other.attendance.config", "部门已存在其他配置规则中"),
    USER_EXIST_IN_OTHER_ATTENDANCE_CONFIG("95036", "user.exist.in.other.attendance.config", "用户已存在其他配置规则中"),
    ATTENDANCE_CONFIG_NAME_EXIST("95037", "attendance.config.name.exist", "日历名称已存在"),
    COUNTRY_NOT_HAVE_DEFAULT_ATTENDANCE_CONFIG("95038", "country.not.have.default.attendance.config", "该国家不存在默认日历"),
    THE_LEAVE_END_TIME_MUST_BE_AFTER_THE_START_TIME("95039", "the.leave.end.time.must.be.after.the.start.time", "请假结束时间必须在开始时间之后"),
    THE_LEAVE_BALANCE_IS_INSUFFICIENT("95040", "the.leave.balance.is.insufficient", "请假余额不足"),
    THE_BALANCE_OF_LEAVE_CANNOT_BE_LESS_THAN_THE_LENGTH_OF_LEAVE("95041", "the.balance.of.leave.cannot.be.less.than.the.length.of.leave", "请假余额不能小于请假时长"),
    THERE_IS_NO_LEAVE_TYPE_INFORMATION_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM("95042", "there.is.no.leave.type.information.for.the.details.of.the.application.form", "申请单详情不存在请假类型信息"),
    THERE_IS_NO_LEAVE_START_OR_END_TIME_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM("95043", "there.is.no.leave.start.or.end.time.for.the.details.of.the.application.form", "申请单详情不存在请假开始或结束时间"),
    THE_USER_LACKS_DETAILED_DATA_FOR_THE_LEAVE_TYPE("95044", "the.user.lacks.detailed.data.for.the.leave.type", "用户该假期类型缺少详情数据"),
    LEAVE_TIME_CANNOT_BE_ZERO("95045", "leave.time.cannot.be.zero", "请假时长不能为0"),
    OVER_TIME_ON_WEEKDAYS_ERROR("95046", "overtime.requests.are.not.allowed.on.weekdays", "这个时间段是正常工作时间！"),
    OVER_TIME_REPEAT_ERROR("95047", "please.do.not.repeat.the.overtime.process", "您已提交过加班单，请勿重复提加班流程！"),
    OVER_TIME_IMPORT_DATA_EMPTY("95048", "the.imported.data.is.empty", "导入的加班数据为空"),
    OVER_TIME_NOT_ON_THE_SAME_DAY("95049", "overtime.days.are.not.on.the.same.day", "加班日期不在同一天"),
    LEAVE_DAY_CANNOT_BE_ZERO("95050", "leave.day.cannot.be.zero", "结束日期未完全覆盖您的排班(跨夜班次)，请往后调整结束日期，谢谢!"),
    OUT_OF_OFFICE_DAYS_EXCEED_7("95051", "out.of.office.is.not.allowed.to.take.more.than.7.days", "按照公司政策，单次外勤申请不能超过7个工作日"),
    OUT_OF_OFFICE_TIME_NOT_EMPTY("95052", "out.of.office.time.can.not.be.zero", "外勤时长不能为0"),
    LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI("95053", "the.leave.duration.cannot.be.less.than.the.minimum.leave.duration.of.the.leave.type", "请假时长不能小于该假期类型最小请假时长"),
    WAREHOUSE_NOT_ALLOW_OUT_OF_WORK("95076", "warehouse.not.allow.out.of.work", "仓内人员暂不支持外勤"),
    WAREHOUSE_NOT_ALLOW_CARD_REVOKE("95077", "warehouse.not.allow.card.revoke", "仓内人员暂不支持撤销补卡"),
    ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR("95081", "attendance.cycle.no.exists", "考勤周期记录不存在"),
    ATTENDANCE_CYCLE_CONFIG_STATUS_NO_CHANGE_ERROR("95082", "attendance.cycle.status.no.change", "考勤周期状态未变化"),
    ATTENDANCE_CYCLE_CONFIG_HAVE_EXISTS_ERROR("95083", "attendance.cycle.config.have.exists", "{0}国家已存在{1}考勤周期，请检查后重新设置！"),
    ATTENDANCE_CYCLE_CONFIG_USER_NOT_HAVE("95084", "user.not.have.attendance.cycle.config", "该员工所在国家没有配置考勤周期方案，考勤周期默认为当月,如非当月请联系HR处理"),
    ATTENDANCE_CYCLE_COUNTRY_NOT_SUPPORT_WEEKLY("95085", "attendance.cycle.country.not.support.week", "{0}国家不支持周维度的考勤周期"),
    ATTENDANCE_CYCLE_CONFIG_GET_CYCLE_TYPE_ERROR("95086", "attendance.cycle.config.get.cycle.type.enum", "考勤周期类型不能为null"),
    COUNTRY_NOT_EMPTY("98035", "country.not.empty", "国家不能为空"),

    PUNCH_CONFIG_NAME_EXIST("96001", "punch.config.name.exist", "打卡规则名称已存在"),
    DEPT_EXIST_IN_OTHER_PUNCH_CONFIG("96002", "dept.exist.in.other.punch.config", "部门已存在其他打卡规则中"),
    USER_EXIST_IN_OTHER_PUNCH_CONFIG("96003", "user.exist.in.other.punch.config", "用户已存在其他打卡规则中"),
    PUNCH_CONFIG_COUNTRY_REPEAT("96004", "punch.config.country.exist", "国家已存在其他打卡规则中"),
    PUNCH_CONFIG_COUNTRY_NOT_EXIST("96005", "punch.config.country.not.exist", "国家级别的打卡规则不存在"),
    PUNCH_CONFIG_COUNTRY_NOT_CHANGE("96006", "punch.config.country.can.not.change", "打卡规则的国家不能修改"),
    PUNCH_CONFIG_DISABLE_NOT_UPDATE("96007", "punch.config.disable.can.not.update", "停用的打卡规则不能编辑"),
    PUNCH_CONFIG_COUNTRY_LEVEL_CAN_NOT_DISABLED("96008", "punch.config.country.level.can.not.disabled", "国家级别的打卡规则不能停用"),
    DISABLED_CONFIG_USER_IN_OTHER_CONFIG("96009", "disabled.config.user.in.other.config", "不可启用，适用范围中的人员已有新规则"),
    COUNTRY_LEVEL_RULE_CAN_NOT_CHANGE_RANGE("96010", "country.level.rule.can.not.change.range", "国家级别的规则不能修改范围"),

    REISSUE_CARD_CONFIG_NAME_EXIST("96011", "reissue.card.config.name.exist", "补卡规则名称已存在"),
    DEPT_EXIST_IN_OTHER_REISSUE_CARD_CONFIG("96012", "dept.exist.in.other.reissue.card.config", "部门已存在其他补卡规则中"),
    USER_EXIST_IN_OTHER_REISSUE_CARD_CONFIG("96013", "user.exist.in.other.reissue.card.config", "用户已存在其他补卡规则中"),
    REISSUE_CARD_CONFIG_COUNTRY_REPEAT("96014", "reissue.card.config.country.exist", "国家已存在其他补卡规则中"),
    REISSUE_CARD_CONFIG_COUNTRY_NOT_CHANGE("96016", "reissue.card.config.country.can.not.change", "补卡规则的国家不能修改"),
    REISSUE_CARD_CONFIG_DISABLE_NOT_UPDATE("96017", "reissue.card.config.disable.can.not.update", "停用的补卡规则不能编辑"),
    REISSUE_CARD_CONFIG_COUNTRY_LEVEL_CAN_NOT_DISABLED("96018", "reissue.card.config.country.level.can.not.disabled", "国家级别的补卡规则不能停用"),


    OVERTIME_CONFIG_NAME_EXIST("96021", "overtime.config.name.exist", "加班规则名称已存在"),
    DEPT_EXIST_IN_OTHER_OVERTIME_CONFIG("96022", "dept.exist.in.other.overtime.config", "部门已存在其他加班规则中"),
    USER_EXIST_IN_OTHER_OVERTIME_CONFIG("96023", "user.exist.in.other.overtime.config", "用户已存在其他加班规则中"),
    OVERTIME_CONFIG_COUNTRY_REPEAT("96024", "overtime.config.country.exist", "国家已存在其他加班规则中"),
    OVERTIME_CONFIG_COUNTRY_NOT_CHANGE("96026", "overtime.config.country.can.not.change", "加班规则的国家不能修改"),
    OVERTIME_CONFIG_COUNTRY_NOT_CHANGE_CONFIG_DISABLE_NOT_UPDATE("96027", "overtime.config.disable.can.not.update", "停用的加班规则不能编辑"),
    OVERTIME_CONFIG_COUNTRY_LEVEL_CAN_NOT_DISABLED("96028", "overtime.country.level.can.not.disabled", "国家级别的加班规则不能停用"),

    /**
     * 移动打卡
     */
    PUNCH_IN_CONTENT_DECRYPT_ERROR("97001", "punch.in.content.decrypt.error", "打卡参数解密失败"),
    CHECK_IN_AGAIN_ERROR("97002", "you.cannot.check.in.again.within.15.seconds", "15s内不能再次打卡哦"),


    /**
     * 假期相关
     */
    COMPANY_LEAVE_REPEAT_ERROR("60001", "company.leave.repeat.error", "公司假期重复"),
    NO_COMPANY_LEAVE_EXISTS_ERROR("60002", "no.company.leave.exists.error", "公司假期不存在"),
    ADD_LEGAL_LEAVE_CONFIG_TIME_ERROR("60005", "the.start.time.of.a.statutory.holiday.cannot.be.greater.than.the.end.time", "法定假期开始时间不能大于结束时间"),
    ADD_LEGAL_LEAVE_CONFIG_NAME_ERROR("60006", "the.name.of.the.statutory.holiday.already.exists", "法定假期名称已经存在"),
    ADD_WELFARE_LEAVE_CONFIG_LEAVE_CONDITION_ERROR("60007", "if.you.select.Require.a.leave.certificate.the.leave.condition.is.required", "选择需要请假证明时，请假条件必填"),
    ADD_WELFARE_LEAVE_CONFIG_CROSS_ERROR("60008", "the.duration.of.statutory.holidays.overlaps.with.those.of.existing.statutory.holidays", "法定假期时间与已有法定假期时间重叠"),
    ADD_WELFARE_LEAVE_CONFIG_TIME_OVERLAP_ERROR("60009", "there.is.a.temporal.overlap.in.statutory.holiday.data", "法定假期数据存在时间重叠"),
    ADD_WELFARE_LEAVE_CONFIG_LEAVE_DISPATCH_COUNTRY_ERROR("60015", "country.of.dispatch.required", "派遣国家必填"),
    ADD_WELFARE_LEAVE_CONFIG_NATIONALITY_ERROR("60017", "the.current.country.does.not.maintain.the.corresponding.nationality.please.maintain.the.nationality.first", "当前国家下没有维护对应国籍，请先维护国籍"),
    ADD_LEGAL_LEAVE_CONFIG_NAME_REPEAT_ERROR("60018", "the.names.of.statutory.holidays.are.repeated", "法定假期名称重复"),
    ADD_CALENDAR_CONFIG_YEAR_ERROR("60019", "the.year.of.the.holiday.cannot.be.empty", "法定假期年份参数不能为空"),
    ADD_CALENDAR_CONFIG_LEGAL_LEAVE_CONFIG_ERROR("60020", "the.fake.details.parameter.cannot.be.empty", "法假详情参数不能为空"),
    ADD_CALENDAR_CONFIG_DETAIL_YEAR_ERROR("60021", "the.calendar.details.data.year.cannot.be.empty", "日历详情数据年份不能为空"),
    ADD_CALENDAR_CONFIG_DETAIL_ERROR("60022", "calendar.details.data.cannot.be.empty", "日历详情数据不能为空"),
    LEAVE_NAME_IS_NOT_EMPTY("60023", "leave.name.is.not.empty", "假期名称不能为空"),
    USER_NOT_HAVE_THIS_LEAVE_TYPE("50453", "user.not.have.this.leave.type", "用户没有该种假期类型"),


    /**
     * 班次相关
     */
    CLASS_TYPE_CANNOT_BE_EMPTY("80000", "classType.cannot.be.empty", "MEX,BRA等国家,班次类型必填"),
    CLASS_NATURE_ILLEGALITY("80001", "classNature.illegality", "班次性质不合法"),
    CLASS_NAME_DUPLICATE("80002", "className.duplicate", "班次名称重复"),
    REST_HOURS_ABNORMAL("80003", "restHours.abnormal", "请修改时间，以满足出勤时长等于工作时长加休息时长"),
    LEGAL_WORKING_HOURS_ABNORMAL("80004", "legalWorkingHours.abnormal", "请修改时间，以满足各时段内工作时长之和等于总工作时长"),
    ATTENDANCE_HOURS_ABNORMAL("80005", "attendanceHours.abnormal", "请修改时间，以满足各时段内出勤时长之和等于总出勤时长"),
    ACCOUNT_MISMATCH("80006", "account.mismatch", "适用人员不匹配"),
    CLASS_INFO_DUPLICATE("80007", "class.info.duplicate", "班次时段信息跟[{0}]班次重复，请勿重复创建，请在原班次基础上调整适用范围"),
    CLASS_NAME_LENGTH_LIMIT_OF_100("80008", "className.length.limit.of.100.characters", "班次名称长度限制100字符"),
    CLASS_NOT_EXISTS("80010", "class.not.exists", "班次不存在"),
    CLASS_STATUS_ALREADY_DISABLED("80011", "class.status.already.disabled", "班次状态已停用,请勿重复操作"),
    CLASS_STATUS_ALREADY_ENABLE("80011", "class.status.already.enable", "班次状态已启用,请勿重复操作"),
    CLASS_ENABLE_CLASS_NAME_DUPLICATE("800012", "class.enable.className.duplicate", "不可启用,同班次名称已有新规则"),
    CLASS_ENABLE_RANGE_DUPLICATE("800013", "class.enable.range.duplicate", "不可启用,适用范围中的人员已有新规则"),
    PROHIBIT_DISABLED_CLASS("800014", "prohibit.disabled.class", "不可以关闭，此班次现有人员在使用"),
    USER_SHIFT_IS_OCCUPIED("800015", "user.shift.is.occupied", "班次适用范围内的用户存在其他未执行完的排班任务,请稍后操作"),
    DISABLE_SHIFT_PROHIBIT_EDIT("800016", "disable.shift.prohibit.editing", "班次停用状态不允许编辑,请先启用班次状态"),
    RULE_RANGE_TYPE_INVALID("8000167", "rule.range.type.invalid", "适用范围类型不合法"),


    SCHEDULING_LIMIT("90000", "scheduling.limit", "排班限制"),
    PUNCH_CLASS_SAVE_NOT_EMPTY("90001", "punch.class.save.not.empty", "排班有效数据不能为空"),
    BATCH_SHIFT_USER_NOT_MATCH("90002", "batch.shift.user.not.match", "批量排班人员不匹配"),
    USER_ARE_OCCUPIED_BY_RUNNING_TASK("90003", "user.are.occupied.by.running.task", "人员被其他正在执行的任务占据"),
    CYCLE_SHIFT_USER_MUST_BELONG_TO_MULTI_CLASS("90004", "cycle.shift.user.must.belong.to.multi.class", "循环排班用户必须属于多班次"),
    CYCLE_SHIFT_USER_CLASS_NOT_MATCH_SELECTED_CLASS("90005", "cycle.shift.user.class.not.match.selected.class", "循环班次用户的班次与所选班次不匹配"),
    CYCLE_SHIFT_USER_CLASS_NOT_HAVE_RELATE_PUNCH_CLASS_RULE("90006", "cycle.shift.user.class.not.have.relate.punch.class.rule", "循环班次用户没有关联任何打卡班次规则"),

    /**
     * 考勤档案相关
     */
    DRIVER_PROHIBIT_UPDATE_ARCHIVE("100000", "dirver.prohibit.update.archive", "司机用户不允许编辑员工档案"),
    MODULE_INVALID("100001", "module.invalid", "变更记录模块不合法"),

    ;


    private final String code;
    private final String desc;
    private final String message;


    public static void main(String[] args) {
        ErrorCodeEnum.REQUEST_HANDLE_FAIL.assertTrue(false, "test");
        throw BizExceptionUtil.ofI18n(ErrorCodeEnum.REQUEST_HANDLE_FAIL);
    }
}
