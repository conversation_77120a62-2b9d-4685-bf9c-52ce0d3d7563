package com.imile.attendance.vacation;

import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;

import java.util.List;

/**
 * 公司假期类型查询
 *
 * <AUTHOR>
 * @Date 2025/4/24
 */
public interface CompanyLeaveConfigManage {
    /**
     * 新增假期
     */
    void addCompanyLeave(CompanyLeaveConfigDO companyLeaveConfigDO,
                         List<CompanyLeaveItemConfigDO> companyLeaveItemConfigDOS,
                         List<UserLeaveDetailDO> userLeaveDetailDOList,
                         List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                         List<UserLeaveStageDetailDO> userLeaveStageDetailDOList,
                         List<UserLeaveStageDetailDO> updateUserLeaveStageDetailDOList,
                         List<UserLeaveRecordDO> recordDOList);

    void addWelfareLeaveConfig(CompanyLeaveConfigDO leaveConfig, List<CompanyLeaveConfigRangDO> leaveConfigRangList
            , CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule, List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList
            , List<CompanyLeaveItemConfigDO> leaveItemConfigList, CompanyLeaveConfigCarryOverDO leaveConfigCarryOver
            , List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList
            , List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList);

    void updateWelfareLeaveConfig(CompanyLeaveConfigDO leaveConfig,
                                  List<CompanyLeaveConfigRangDO> leaveConfigDeleteRangList,
                                  List<CompanyLeaveConfigRangDO> leaveConfigRangList);

    void updateStatus(CompanyLeaveConfigDO leaveConfig,
                      List<UserLeaveDetailDO> userLeaveDetailList);

    /**
     * 新增或修改数据
     *
     * @param configDO
     * @return
     */
    boolean saveOrUpdate(CompanyLeaveConfigDO configDO);

    /**
     * 批量新增公司假期阶段信息
     *
     * @param itemConfigDOList
     * @return
     */
    boolean batchSaveItem(List<CompanyLeaveItemConfigDO> itemConfigDOList);

    /**
     * 处理用户假期绑定范围
     *
     * @param addLeaveRang
     * @param updateLeaveRang
     */
    void handlerUserLeaveConfigRange(List<CompanyLeaveConfigRangDO> addLeaveRang,
                                     List<CompanyLeaveConfigRangDO> updateLeaveRang);

    /**
     * 处理用户历史假期绑定范围
     *
     * @param addUserLeaveHistoryList
     * @param updateUserLeaveHistoryList
     */
    void handlerUserLeaveConfigHistoryRange(List<UserLeaveConfigHistoryDO> addUserLeaveHistoryList,
                                            List<UserLeaveConfigHistoryDO> updateUserLeaveHistoryList);

    /**
     * 查询所有启用的假期配置
     */
    List<CompanyLeaveConfigDO> selectAllActive();

}
