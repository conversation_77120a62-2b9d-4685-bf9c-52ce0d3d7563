package com.imile.attendance.vacation;

import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-05-23
 */
public interface UserLeaveDetailManage {

    void userLeaveMinutesInitUpdate(List<UserLeaveStageDetailDO> updateDetailList,
                                    List<UserLeaveRecordDO> updateRecordList);


    void userLeaveBalanceDaysUpdate(List<UserLeaveDetailDO> addUserLeaveDetailList,
                                    List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                    List<UserLeaveRecordDO> addUserLeaveRecordList,
                                    List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                    List<UserLeaveDetailDO> updateUserLeaveDetailDOList);

    /**
     * 查询用户假期信息
     *
     */
    List<UserLeaveDetailDO> listByUserId(List<Long> userIdList);

}
