package com.imile.attendance.employee;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.common.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25
 * @Description todo 可优化到infrastructure层
 */
@Component
public class UserInfoManage {

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;


    /**
     * 根据用户id获取用户的部门等属性(部门，岗位在rpc获取)
     */
    public UserInformationDTO getUserInfoInformation(Long userId) {
        if (null == userId) {
            return null;
        }
        UserInformationDTO userInfoInformation = userInfoDao.getUserInfoInformation(userId);
        if (null == userInfoInformation) {
            return null;
        }
        Long deptId = userInfoInformation.getDeptId();
        if (null != deptId) {
            AttendanceDept attendanceDept = deptService.getByDeptId(deptId);
            if (null != attendanceDept) {
                userInfoInformation.setDeptName(attendanceDept.getDeptNameCn());
                userInfoInformation.setDeptNameEn(attendanceDept.getDeptNameEn());
            }
        }
        Long postId = userInfoInformation.getPostId();
        if (null != postId) {
            AttendancePost attendancePost = postService.getByPostId(postId);
            userInfoInformation.setPostName(attendancePost.getPostNameCn());
            userInfoInformation.setPostNameEn(attendancePost.getPostNameEn());
        }
        return userInfoInformation;
    }

    public List<UserInfoDO> selectByDeptIdsAndClassNature(String country, List<Long> deptIds, String classNature) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }

        List<String> employeeTypeList = CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(country)
                ? EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT
                : EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT;

        UserDaoQuery userQuery = UserDaoQuery.builder()
                .locationCountry(country)
                .deptIds(deptIds)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .employeeTypes(employeeTypeList)
                .classNature(classNature)
                .build();
        return userInfoDao.userList(userQuery);
    }

    public List<UserInfoDO> selectByUserCodes(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        UserDaoQuery userQuery = UserDaoQuery.builder()
                .userCodes(userCodes)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        return userInfoDao.userList(userQuery);
    }

    public List<UserInfoDO> selectByUserIdsAndClassNature(List<Long> userIds, String classNature) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        UserDaoQuery userQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .classNature(classNature)
                .build();
        return userInfoDao.userList(userQuery);
    }

    public List<UserInfoDO> selectByCountryAndClassNature(String country, String classNature) {
        if (StringUtils.isEmpty(country)) {
            return Collections.emptyList();
        }
        List<String> employeeTypeList = CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(country)
                ? EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT
                : EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT;

        UserDaoQuery userQuery = UserDaoQuery.builder()
                .locationCountry(country)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .employeeTypes(employeeTypeList)
                .classNature(classNature)
                .build();
        return userInfoDao.userList(userQuery);
    }

    public List<UserInfoDO> selectByCountryList(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        UserDaoQuery userQuery = UserDaoQuery.builder()
                .locationCountryList(countryList)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        return userInfoDao.userList(userQuery);
    }
}
