package com.imile.attendance.rule;

import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.ReissueCardConfigRangeByDateQuery;
import com.imile.attendance.rule.bo.CountryReissueCardConfig;
import com.imile.attendance.rule.bo.ReissueCardConfigBO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
public interface ReissueCardConfigManage {

    void configRangeUpdateOrAdd(List<ReissueCardConfigRangeDO> updateList, List<ReissueCardConfigRangeDO> addList);

    void configUpdateAndAdd(ReissueCardConfigDO updateConfig, ReissueCardConfigDO addConfig);

    //todo 性能问题
    void configUpdateAndAdd(ReissueCardConfigDO updateConfig,
            ReissueCardConfigDO addConfig,
            List<ReissueCardConfigRangeDO> updatedConfigRanges,
            List<ReissueCardConfigRangeDO> addConfigRanges);

    /**
     * 根据主键获取补卡规则配置
     */
    List<ReissueCardConfigDO> getReissueCardConfigByIds(List<Long> reissueCardConfigIdList);

    /**
     * 获取补卡规则BO
     * 
     * @param configNo 配置编号
     * @return 补卡规则BO
     */
    ReissueCardConfigBO getConfigBO(String configNo);

    /**
     * 获取国家的补卡规则
     * 
     * @param country 国家
     * @return 国家的补卡规则
     */
    CountryReissueCardConfig getCountryConfig(String country);

    /**
     * 批量获取国家的补卡规则列表
     * 
     * @param countries 国家列表
     * @return 国家的补卡规则列表
     */
    List<CountryReissueCardConfig> getCountryListConfig(List<String> countries);


    /**
     * 根据用户ID列表获取补卡规则
     *
     * @param userIds 用户ID列表
     * @return 补卡规则Map
     */
    Map<Long, ReissueCardConfigDO> getConfigMapByUserIdList(List<Long> userIds);

    /**
     * 根据用户ID列表和指定时间获取补卡规则
     * 不过滤是否最新和状态
     * key: userId
     */
    Map<Long, ReissueCardConfigDO> mapByUserIds(List<Long> userIds, Date endDate);

    /**
     * 查询用户所有补卡规则
     */
    List<RuleConfigModifyDTO> selectAllByBizId(Long bizId);

    /**
     * 查询员工指定时间内的打卡规则
     */
    List<ReissueCardConfigRangeDO> selectConfigRangeByDate(ReissueCardConfigRangeByDateQuery query);

}
