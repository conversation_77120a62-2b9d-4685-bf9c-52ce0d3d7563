package com.imile.attendance.form;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceUserCardConfigDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9
 * @Description 请假/外勤等单据manage
 */
@Component
public class AttendanceFormManage {

    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;
    @Resource
    private AttendanceFormRelationDao attendanceFormRelationDao;
    @Resource
    private AttendanceUserCardConfigDao userCardConfigDao;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;

    // --- 拷贝原有HRMS查询方法
    public List<AttendanceFormDO> selectForm(ApplicationFormQuery query) {
        return attendanceFormDao.selectForm(query);
    }


    public List<AttendanceFormDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return attendanceFormDao.listByIds(idList);
    }

    public AttendanceFormDetailBO getFormDetailById(Long formId) {
        if (formId == null) {
            return null;
        }
        AttendanceFormDO formDO = attendanceFormDao.getById(formId);
        List<AttendanceFormAttrDO> attrDOList = attendanceFormAttrDao.selectFormAttrByFormIdLit(Collections.singletonList(formId));
        List<AttendanceFormRelationDO> relationDOList = attendanceFormRelationDao.selectRelationByFormIdList(Collections.singletonList(formId));
        return AttendanceFormDetailBO.of(
                formDO,
                attrDOList,
                relationDOList
        );
    }

    public AttendanceFormDetailBO getFormDetailByCode(String applicationFormCode) {
        if (StringUtils.isBlank(applicationFormCode)) {
            return null;
        }
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setApplicationFormCode(applicationFormCode);
        List<AttendanceFormDO> applicationFormDOList = attendanceFormDao.selectForm(applicationFormQuery);
        if (CollectionUtils.isEmpty(applicationFormDOList)) {
            return null;
        }
        AttendanceFormDO attendanceFormDO = applicationFormDOList.get(0);
        List<AttendanceFormAttrDO> attrDOList =
                attendanceFormAttrDao.selectFormAttrByFormIdLit(Collections.singletonList(attendanceFormDO.getId()));
        List<AttendanceFormRelationDO> relationDOList =
                attendanceFormRelationDao.selectRelationByFormIdList(Collections.singletonList(attendanceFormDO.getId()));
        return AttendanceFormDetailBO.of(
                attendanceFormDO,
                attrDOList,
                relationDOList
        );
    }

    public List<AttendanceFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query) {
        return attendanceFormDao.selectAttendanceApprovalInfo(query);
    }

    public List<AttendanceFormAttrDO> selectFormAttrByFormIdLit(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return attendanceFormAttrDao.selectFormAttrByFormIdLit(formIdList);
    }

    public List<AttendanceFormAttrDO> selectFormAttrByFormId(Long formId) {
        return attendanceFormAttrDao.selectFormAttrByFormId(formId);
    }

    public List<AttendanceFormRelationDO> selectRelationByFormIdList(List<Long> formIdList) {
        return attendanceFormRelationDao.selectRelationByFormIdList(formIdList);
    }

    public List<AttendanceFormRelationDO> selectRelationByRelationIdList(List<Long> relationIdList) {
        return attendanceFormRelationDao.selectRelationByRelationIdList(relationIdList);
    }

    // --- manage操作
    @Transactional(rollbackFor = Exception.class)
    public void delete(AttendanceFormDO formDO,
                       List<AttendanceFormRelationDO> relationDOList,
                       List<AttendanceFormAttrDO> formAttrDOList) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOList)) {
            attendanceFormRelationDao.updateBatchById(relationDOList);
        }
        if (CollectionUtils.isNotEmpty(formAttrDOList)) {
            attendanceFormAttrDao.updateBatchById(formAttrDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMqForm(AttendanceFormDO formDO,
                             UserCycleReissueCardCountDO userCardConfigDO,
                             EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                             List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                             UserLeaveRecordDO userLeaveRecord) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (userCardConfigDO != null) {
            userCardConfigDao.updateById(userCardConfigDO);
        }
        if (abnormalAttendanceDO != null) {
            employeeAbnormalAttendanceDao.updateById(abnormalAttendanceDO);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailInfoList)) {
            userLeaveStageDetailDao.updateBatchById(userLeaveStageDetailInfoList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            userLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalFormConfirmCycle(List<AttendanceFormDO> updateFormList,
                                               List<AttendanceFormAttrDO> addAttrList,
                                               List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                               List<EmployeeAbnormalOperationRecordDO> addRecordList) {
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addAttrList)) {
            attendanceFormAttrDao.saveBatch(addAttrList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            employeeAbnormalAttendanceDao.updateBatchById(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(addRecordList)) {
            employeeAbnormalOperationRecordDao.saveBatch(addRecordList);
        }
    }

    public List<AttendanceFormDetailBO> listByUserIds(List<Long> userIdList, List<String> formStatusList, List<String> formTypeList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(formStatusList);
        applicationFormQuery.setFromTypeList(formTypeList);
        List<AttendanceFormDO> attendanceFormDOList = attendanceFormDao.selectForm(applicationFormQuery);
        if (CollectionUtils.isEmpty(attendanceFormDOList)) {
            return Collections.emptyList();
        }
        List<Long> formIdList = attendanceFormDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        Map<Long, List<AttendanceFormAttrDO>> formAttrMap = attendanceFormAttrDao.selectFormAttrByFormIdLit(formIdList)
                .stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        List<AttendanceFormDetailBO> result = new ArrayList<>();
        for (AttendanceFormDO attendanceFormDO : attendanceFormDOList) {
            AttendanceFormDetailBO attendanceFormDetailBO = AttendanceFormDetailBO.of(attendanceFormDO, formAttrMap.get(attendanceFormDO.getId()), null);
            result.add(attendanceFormDetailBO);
        }
        return result;
    }
}
