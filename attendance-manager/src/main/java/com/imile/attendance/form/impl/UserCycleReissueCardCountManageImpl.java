package com.imile.attendance.cycleConfig.impl;

import com.imile.attendance.cycleConfig.UserCycleReissueCardCountManage;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.UserCycleReissueCardCountDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Component
public class UserCycleReissueCardCountManageImpl implements UserCycleReissueCardCountManage {

    @Resource
    private UserCycleReissueCardCountDao userCycleReissueCardCountDao;

    @Override
    public List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList) {
        return userCycleReissueCardCountDao.selectByUserIdList(userIdList);
    }

    @Override
    public void batchUpdateById(List<UserCycleReissueCardCountDO> userCardConfigDOList) {
        if (CollectionUtils.isEmpty(userCardConfigDOList)) {
            return;
        }
        userCycleReissueCardCountDao.updateBatchById(userCardConfigDOList);
    }

    @Override
    public void batchSave(List<UserCycleReissueCardCountDO> userCardConfigDOList) {
        if (CollectionUtils.isEmpty(userCardConfigDOList)) {
            return;
        }
        userCycleReissueCardCountDao.saveBatch(userCardConfigDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initCardUpdate(List<UserCycleReissueCardCountDO> addUserCardConfigDOList,
                               List<UserCycleReissueCardCountDO> updateUserCardConfigDOList) {
        if (CollectionUtils.isNotEmpty(addUserCardConfigDOList)) {
            userCycleReissueCardCountDao.saveBatch(addUserCardConfigDOList);
        }
        if (CollectionUtils.isNotEmpty(updateUserCardConfigDOList)) {
            userCycleReissueCardCountDao.updateBatchById(updateUserCardConfigDOList);
        }
    }
}
