package com.imile.attendance.shift.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.shift.ShiftStatusEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.shift.dao.UserCycleShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
@Component
public class UserShiftConfigManageImpl implements UserShiftConfigManage {

    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private UserCycleShiftConfigDao userCycleShiftConfigDao;

    @Override
    public List<UserShiftConfigDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList) {
        return userShiftConfigDao.selectBatchUserRecord(userIdList, dayIdList);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId) {
        return userShiftConfigDao.selectRecordByUserIdList(userIdList, startDayId, endDayId);
    }

    @Override
    public List<UserShiftConfigDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId, Long endDayId) {
        return userShiftConfigDao.selectRecordByUserIdListAndClassId(userIdList, classId, startDayId, endDayId);
    }

    @Override
    @Transactional
    public void batchShiftUpdateOrAdd(List<UserShiftConfigDO> oldUserShiftConfigDOList,
                                      List<UserShiftConfigDO> newUserShiftConfigDOList) {
        if (CollectionUtils.isNotEmpty(oldUserShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserShiftConfigDO>> partitionList = Lists.partition(oldUserShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userShiftConfigDao.updateBatchById(list));
        }
        if (CollectionUtils.isNotEmpty(newUserShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserShiftConfigDO>> partitionList = Lists.partition(newUserShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userShiftConfigDao.saveBatch(list));
        }
    }

    @Override
    public void updateToOld(Long userId, Date date) {
        userShiftConfigDao.updateToOld(userId, date);
    }

    @Override
    @Transactional
    public void cycleShiftSave(List<UserShiftConfigDO> oldUserShiftConfigDOList, List<UserShiftConfigDO> newUserShiftConfigDOList, List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList, List<UserCycleShiftConfigDO> addCycleShiftConfigDOList) {
        if (CollectionUtils.isNotEmpty(oldUserShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserShiftConfigDO>> partitionList = Lists.partition(oldUserShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userShiftConfigDao.updateBatchById(list));
        }
        if (CollectionUtils.isNotEmpty(newUserShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserShiftConfigDO>> partitionList = Lists.partition(newUserShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userShiftConfigDao.saveBatch(list));
        }
        if (CollectionUtils.isNotEmpty(updateCycleShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserCycleShiftConfigDO>> partitionList = Lists.partition(updateCycleShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userCycleShiftConfigDao.updateBatchById(list));
        }
        if (CollectionUtils.isNotEmpty(addCycleShiftConfigDOList)) {
            // 超过最大批量处理数量进行分组
            List<List<UserCycleShiftConfigDO>> partitionList = Lists.partition(addCycleShiftConfigDOList, BusinessConstant.MAX_BATCH_SIZE);
            partitionList.forEach(list ->
                    userCycleShiftConfigDao.saveBatch(list));
        }
    }

    @Override
    @Transactional
    public void cancelCycleShift(List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList, List<UserShiftConfigDO> oldUserClassConfigDOList) {
        if (CollectionUtils.isNotEmpty(updateCycleShiftConfigDOList)) {
            userCycleShiftConfigDao.updateBatchById(updateCycleShiftConfigDOList);
        }
        if (CollectionUtils.isNotEmpty(oldUserClassConfigDOList)) {
            userShiftConfigDao.updateBatchById(oldUserClassConfigDOList);
        }
    }

    @Override
    public List<Long> getUserIdsByPunchClassIdAndDayId(Long punchClassId, Long dayId) {
        if (null == punchClassId || null == dayId) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .eq(UserShiftConfigDO::getPunchClassConfigId, punchClassId)
                .eq(UserShiftConfigDO::getDayId, dayId)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyList();
        }
        return userShiftConfigDOList.stream()
                .map(UserShiftConfigDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getUserIdsByPunchClassIdAndAfterDayId(Long punchClassId, Long dayId) {
        if (null == punchClassId || null == dayId) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .eq(UserShiftConfigDO::getPunchClassConfigId, punchClassId)
                .gt(UserShiftConfigDO::getDayId, dayId)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyList();
        }
        return userShiftConfigDOList.stream()
                .map(UserShiftConfigDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<UserShiftConfigDO>> getShiftConfigByUserIdsAndDayIdIncludeAfter(List<Long> userIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds) || null == dayId) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .in(UserShiftConfigDO::getUserId, userIds)
                .ge(UserShiftConfigDO::getDayId, dayId)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyMap();
        }
        return userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
    }

    @Override
    public Map<Long, List<UserShiftConfigDO>> getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(List<Long> userIds, List<Long> classIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds) || null == dayId) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .in(UserShiftConfigDO::getUserId, userIds)
                .in(UserShiftConfigDO::getPunchClassConfigId, classIds)
                .ge(UserShiftConfigDO::getDayId, dayId)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyMap();
        }
        return userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
    }

    @Override
    public Map<Long, List<UserShiftConfigDO>> getConfigByUserIdsAndDayIdIncludeAfter(List<Long> userIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds) || null == dayId) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .in(UserShiftConfigDO::getUserId, userIds)
                .ge(UserShiftConfigDO::getDayId, dayId)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyMap();
        }
        return userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
    }

    @Override
    public Map<Long, List<UserShiftConfigDO>> getAutoShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(List<Long> userIds, List<Long> classIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(classIds) || null == dayId) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class)
                .eq(UserShiftConfigDO::getShiftType, ShiftTypeEnum.AUTO_SHIFT.getCode())
                .in(UserShiftConfigDO::getUserId, userIds)
                .ge(UserShiftConfigDO::getDayId, dayId)
                .in(UserShiftConfigDO::getPunchClassConfigId, classIds)
                .eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y)
                .eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Collections.emptyMap();
        }
        return userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
    }

    @Override
    public Map<Long, List<UserShiftConfigDO>> queryForUserShiftPage(Long startDayId, Long endDayId, String shiftStatus) {
        if (null == startDayId || null == endDayId) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "startDayId or endDayId is null");
        }
        //先处理时间范围和排班类型
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class);
        //这里是大于等于
        queryWrapper.ge(UserShiftConfigDO::getDayId, startDayId);
        //这里是小于等于
        queryWrapper.le(UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        List<UserShiftConfigDO> list = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        //排班情况:判断日期范围内，若均已排班，则为已排班；若只有要一天未排班，则为未排班
        if (StringUtils.isBlank(shiftStatus)) {
            return list.stream()
                    .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
        }

        // 获取日期范围内所有日期
        List<Long> dayIdList = DateHelper.getDayIdList(startDayId, endDayId);
        int totalDays = dayIdList.size();

        // 按用户ID分组
        Map<Long, List<UserShiftConfigDO>> userShiftMap = list.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        Map<Long, List<UserShiftConfigDO>> result = new HashMap<>();
        userShiftMap.forEach((userId, userShiftConfigDOList) -> {
            if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                return;
            }
            // 获取用户在每个日期的排班情况
            Map<Long, UserShiftConfigDO> userShiftDayMap = userShiftConfigDOList.stream()
                    .collect(Collectors.toMap(UserShiftConfigDO::getDayId, Function.identity(), (oldVal, newVal) -> oldVal));

            // 统计用户已排班的天数
            long shiftedDaysCount = userShiftDayMap.keySet().stream()
                    .filter(dayIdList::contains)
                    .count();

            if (StringUtils.equals(shiftStatus, ShiftStatusEnum.SHIFTED.getCode())) {
                // 已排班：日期范围内每一天都已排班
                if (shiftedDaysCount == totalDays) {
                    result.put(userId, userShiftConfigDOList);
                }
            } else if (StringUtils.equals(shiftStatus, ShiftStatusEnum.UN_SHIFTED.getCode())) {
                // 未排班：日期范围内至少有一天未排班
                if (shiftedDaysCount < totalDays) {
                    result.put(userId, userShiftConfigDOList);
                }
            }
        });
        return result;
    }

    @Override
    public List<UserShiftConfigDO> selectUserShift(Long classId, Long startDayId, Long endDayId) {
        return userShiftConfigDao.selectUserShift(classId, startDayId, endDayId);
    }

    @Override
    public List<UserShiftConfigDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds) {
        return userShiftConfigDao.selectUserShiftByDayIds(userId, dayIds);
    }

    @Override
    public List<Long> getAllHasShiftedUsersInDayRange(Long startDayId, Long endDayId) {
        if (null == startDayId || null == endDayId) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "startDayId or endDayId is null");
        }
        //先处理时间范围和排班类型
        LambdaQueryWrapper<UserShiftConfigDO> queryWrapper = Wrappers.lambdaQuery(UserShiftConfigDO.class);
        //这里是大于等于
        queryWrapper.ge(UserShiftConfigDO::getDayId, startDayId);
        //这里是小于等于
        queryWrapper.le(UserShiftConfigDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigDO::getIsLatest, BusinessConstant.Y);
        List<UserShiftConfigDO> list = userShiftConfigDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 获取日期范围内所有日期
        List<Long> dayIdList = DateHelper.getDayIdList(startDayId, endDayId);
        int totalDays = dayIdList.size();

        // 按用户ID分组
        Map<Long, List<UserShiftConfigDO>> userShiftMap = list.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        List<Long> userIdList = new ArrayList<>();
        userShiftMap.forEach((userId, userShiftConfigDOList) -> {
            if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                return;
            }
            // 获取用户在每个日期的排班情况
            Map<Long, UserShiftConfigDO> userShiftDayMap = userShiftConfigDOList.stream()
                    .collect(Collectors.toMap(UserShiftConfigDO::getDayId, Function.identity(), (oldVal, newVal) -> oldVal));

            // 统计用户已排班的天数
            long shiftedDaysCount = userShiftDayMap.keySet().stream()
                    .filter(dayIdList::contains)
                    .count();

            if (shiftedDaysCount == totalDays) {
                userIdList.add(userId);
            }
        });
        return userIdList;
    }
}
